<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课前提问工具 - 功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .demo-section {
            margin: 30px 0;
            padding: 25px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.3em;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        }
        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        .btn.info {
            background: linear-gradient(135deg, #3498db 0%, #74b9ff 100%);
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .mock-toolbar {
            background: #2c3e50;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            min-height: 64px;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .feature-card h4 {
            margin-top: 0;
            color: #495057;
        }
        .api-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 课前提问工具</h1>
            <p>集成签到与课前提问功能的TCIC自定义工具</p>
        </div>

        <div class="content">
            <div id="status" class="status info">
                <strong>状态：</strong>正在初始化...
            </div>

            <div class="demo-section">
                <h3>🚀 快速测试</h3>
                <button class="btn success" onclick="testQuestionModal()">📋 测试问题显示</button>
                <button class="btn info" onclick="testSignInModal()">✅ 测试签到功能</button>
                <button class="btn" onclick="directTestQuestion()">🎯 直接测试问题弹窗</button>
                <button class="btn" onclick="loadCustomScript()">🔄 重新加载组件</button>
            </div>

            <div class="demo-section">
                <h3>🎯 功能特性</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📝 课前提问显示</h4>
                        <ul>
                            <li>从API获取学生提问</li>
                            <li>支持文本和图片内容</li>
                            <li>实时刷新功能</li>
                            <li>响应式设计</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>✅ 签到功能</h4>
                        <ul>
                            <li>老师发起签到</li>
                            <li>学生响应签到</li>
                            <li>实时状态反馈</li>
                            <li>防重复签到</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h3>🔧 API接口信息</h3>
                <div class="api-info">
                    <strong>接口地址：</strong><br>
                    <code>GET https://api.kaohaolaai.com/api/b/appoint/teacher/pre/question?classid={classId}</code>
                </div>
                <div class="code-block">
{
  "code": 100000,
  "msg": ["操作成功"],
  "data": [
    [
      {
        "message": "老师你好，我是李洛伊",
        "message_type": "text",
        "content": "老师你好，我是李洛伊",
        "role": "user",
        "is_system": "2"
      }
    ]
  ]
}
                </div>
            </div>

            <div class="demo-section">
                <h3>🖥️ 模拟老师工具栏</h3>
                <div class="header-component">
                    <div class="header__sub-operation mock-toolbar" id="mockToolbar">
                        <span style="color: #bdc3c7; margin-right: 20px;">工具栏区域 →</span>
                        <!-- 自定义组件将插入到这里 -->
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h3>📱 集成方式</h3>
                <p>在TCIC课堂URL后添加以下参数：</p>
                <div class="code-block">
&debugjs=http://localhost:3001/custom.js
                </div>
                <p><strong>完整示例：</strong></p>
                <div class="code-block">
https://your-tcic-classroom-url.com/classroom?classid=123456&debugjs=http://localhost:3001/custom.js
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟TCIC SDK环境
        window.TCIC = {
            SDK: {
                instance: {
                    isTeacher: () => true,
                    isStudent: () => false,
                    getUserId: () => 'teacher_demo_123',
                    getClassInfo: () => ({ classId: '245783322' }),
                    promiseState: (state, value) => {
                        console.log('TCIC promiseState:', state, value);
                        return Promise.resolve();
                    },
                    on: (event, callback) => {
                        console.log('TCIC event listener added:', event);
                    },
                    updateTask: (taskId, content) => {
                        console.log('TCIC Task updated:', taskId, content);
                        updateStatus('success', `任务更新: ${taskId}`);
                    }
                }
            }
        };

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.innerHTML = `<strong>状态：</strong>${message}`;
        }

        function testQuestionModal() {
            if (window.TCIC && window.TCIC.SDK) {
                // 直接调用老师按钮的点击事件
                const teacherBtn = document.querySelector('teacher-checkin-btn');
                if (teacherBtn) {
                    // 模拟点击提问按钮
                    const questionBtn = teacherBtn.shadowRoot?.querySelector('.custom-question-btn') ||
                                       teacherBtn.querySelector('.custom-question-btn');
                    if (questionBtn) {
                        questionBtn.click();
                        updateStatus('info', '已点击提问按钮');
                    } else {
                        updateStatus('error', '找不到提问按钮');
                    }
                } else {
                    updateStatus('error', '找不到老师工具栏组件');
                }
            } else {
                updateStatus('error', 'TCIC SDK未加载');
            }
        }

        function testSignInModal() {
            if (window.TCIC && window.TCIC.SDK) {
                // 模拟触发签到
                const event = new CustomEvent('tcic-check-in', {
                    detail: { type: 'ask-check-in' }
                });
                document.dispatchEvent(event);
                updateStatus('info', '已触发签到功能');
            } else {
                updateStatus('error', 'TCIC SDK未加载');
            }
        }

        function directTestQuestion() {
            // 直接创建和显示问题弹窗，绕过任务系统
            updateStatus('info', '直接测试问题弹窗...');

            const questionModal = document.querySelector('question-modal');
            if (questionModal) {
                // 直接调用组件的内部方法
                const vueInstance = questionModal.__vueParentComponent?.ctx || questionModal._vueInstance;
                if (vueInstance) {
                    // 模拟问题数据
                    vueInstance.questions = [
                        [
                            {
                                message: "这是第一个测试问题",
                                message_type: "text",
                                content: "这是第一个测试问题",
                                role: "user",
                                is_system: "2"
                            }
                        ],
                        [
                            {
                                message: "这是第二个测试问题",
                                message_type: "text",
                                content: "这是第二个测试问题",
                                role: "user",
                                is_system: "2"
                            }
                        ]
                    ];
                    vueInstance.showModal = true;
                    vueInstance.currentQuestionIndex = 0;
                    updateStatus('success', '问题弹窗已显示（测试数据）');
                } else {
                    updateStatus('error', '无法访问问题组件实例');
                }
            } else {
                updateStatus('error', '找不到问题组件');
            }
        }

        function loadCustomScript() {
            updateStatus('info', '正在加载自定义组件...');

            // 移除已存在的脚本
            const existingScript = document.querySelector('script[src*="custom.js"]');
            if (existingScript) {
                existingScript.remove();
            }

            // 清空工具栏
            const toolbar = document.getElementById('mockToolbar');
            const customElements = toolbar.querySelectorAll('teacher-checkin-btn, question-modal, student-modal');
            customElements.forEach(el => el.remove());

            // 加载新脚本
            const script = document.createElement('script');
            script.src = 'http://localhost:3001/custom.js?' + Date.now(); // 添加时间戳避免缓存
            script.onload = () => {
                updateStatus('success', '自定义组件加载成功！');

                // 等待一下让组件初始化
                setTimeout(() => {
                    const teacherBtn = toolbar.querySelector('teacher-checkin-btn');
                    if (teacherBtn) {
                        updateStatus('success', '老师工具栏组件已添加');
                    }
                }, 500);
            };
            script.onerror = () => {
                updateStatus('error', '无法加载自定义组件，请确保开发服务器正在运行');
            };
            document.head.appendChild(script);
        }

        // 页面加载完成后自动加载组件
        window.addEventListener('load', () => {
            setTimeout(loadCustomScript, 1000);
        });
    </script>
</body>
</html>
