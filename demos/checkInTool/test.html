<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课前提问工具测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .header-component {
            background: #2c3e50;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .header__sub-operation {
            display: flex;
            align-items: center;
            height: 64px;
        }
        .mock-tcic {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 课前提问工具演示</h1>
            <p>这是一个集成了签到和课前提问功能的TCIC自定义工具</p>
        </div>

        <div class="demo-section">
            <h3>📋 功能说明</h3>
            <ul>
                <li><strong>签到功能</strong>：老师可以发起签到，学生收到签到弹窗</li>
                <li><strong>课前提问</strong>：显示学生的课前提问内容（文本和图片）</li>
                <li><strong>实时通信</strong>：基于TCIC SDK的Task系统实现师生互动</li>
                <li><strong>API集成</strong>：从 api.kaohaolaai.com 获取课前提问数据</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>🔧 集成方式</h3>
            <p>在课堂URL后添加参数：</p>
            <code style="background: #f8f9fa; padding: 5px; border-radius: 3px;">
                &debugjs=http://localhost:3001/custom.js
            </code>
        </div>

        <div class="demo-section">
            <h3>🎯 API接口</h3>
            <div class="mock-tcic">
                <strong>接口地址：</strong><br>
                <code>GET https://api.kaohaolaai.com/api/b/appoint/teacher/pre/question?classid={classId}</code><br><br>
                <strong>返回格式：</strong><br>
                <pre style="background: white; padding: 10px; border-radius: 3px; overflow-x: auto;">
{
  "code": 100000,
  "msg": ["操作成功"],
  "data": [
    [
      {
        "message": "老师你好，我是李洛伊",
        "message_type": "text",
        "content": "老师你好，我是李洛伊",
        "role": "user",
        "is_system": "2"
      },
      {
        "message": "https://example.com/image.png",
        "message_type": "image", 
        "content": "https://example.com/image.png",
        "role": "user",
        "is_system": "2"
      }
    ]
  ]
}
                </pre>
            </div>
        </div>

        <div class="demo-section">
            <h3>🖥️ 模拟界面</h3>
            <p>以下是老师端工具栏的模拟效果：</p>
            <div class="header-component">
                <div class="header__sub-operation" id="mockToolbar">
                    <!-- 这里会插入自定义组件 -->
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🧪 测试功能</h3>
            <button class="btn" onclick="testQuestionModal()">测试问题显示</button>
            <button class="btn" onclick="testSignIn()">测试签到功能</button>
            <button class="btn" onclick="openDevTools()">打开开发者工具</button>
        </div>

        <div class="demo-section">
            <h3>📝 使用说明</h3>
            <ol>
                <li>确保项目在 http://localhost:3001 运行</li>
                <li>在TCIC课堂中添加 debugjs 参数</li>
                <li>老师端会显示"签到"和"提问"两个按钮</li>
                <li>点击"提问"按钮会显示课前提问弹窗</li>
                <li>学生端会收到相应的交互提示</li>
            </ol>
        </div>
    </div>

    <!-- 模拟TCIC SDK -->
    <script>
        // 模拟TCIC SDK环境
        window.TCIC = {
            SDK: {
                instance: {
                    isTeacher: () => true,
                    isStudent: () => false,
                    getUserId: () => 'teacher_123',
                    getClassInfo: () => ({ classId: '245783322' }),
                    promiseState: (state, value) => Promise.resolve(),
                    on: (event, callback) => {},
                    updateTask: (taskId, content) => {
                        console.log('Task updated:', taskId, content);
                    }
                }
            }
        };

        // 测试函数
        function testQuestionModal() {
            alert('在实际TCIC环境中，这会触发问题显示弹窗');
        }

        function testSignIn() {
            alert('在实际TCIC环境中，这会发起签到请求');
        }

        function openDevTools() {
            window.open('http://localhost:3001', '_blank');
        }

        // 页面加载完成后加载自定义组件
        window.addEventListener('load', () => {
            const script = document.createElement('script');
            script.src = 'http://localhost:3001/custom.js';
            script.onload = () => {
                console.log('自定义组件已加载');
            };
            script.onerror = () => {
                console.error('无法加载自定义组件，请确保开发服务器正在运行');
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
