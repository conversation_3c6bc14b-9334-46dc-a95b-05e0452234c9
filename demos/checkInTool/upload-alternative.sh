#!/bin/bash

# 课前提问工具 - 替代上传方案
# 当SSH密钥问题暂时无法解决时使用

echo "🚀 课前提问工具 - 替代上传方案"
echo "================================"

# 检查构建文件
if [ ! -f "dist/custom.js" ]; then
    echo "❌ 构建文件不存在，正在构建..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ 构建失败"
        exit 1
    fi
fi

echo "✅ 构建文件存在: $(ls -lh dist/custom.js | awk '{print $5}')"

echo ""
echo "📋 由于SSH密钥问题，请选择以下替代方案："
echo ""

echo "方案1: 🌐 使用在线文件传输"
echo "   1. 访问 https://wetransfer.com/ 或类似服务"
echo "   2. 上传 dist/custom.js 文件"
echo "   3. 获取下载链接"
echo "   4. 在服务器上使用 wget 下载"
echo ""

echo "方案2: 📱 使用云服务器控制台"
echo "   1. 登录云服务器控制台"
echo "   2. 使用文件管理器或VNC"
echo "   3. 直接上传文件"
echo ""

echo "方案3: 🔧 修复SSH密钥后使用"
echo "   1. 在云服务器控制台添加SSH密钥"
echo "   2. 密钥内容："
echo "   ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIP0YVHWQWZasoBZuHmZUoiIMGtS68w+L83cod65sITmW <EMAIL>"
echo "   3. 然后运行: ./upload.sh root@************* /var/www/html/"
echo ""

echo "方案4: 📦 生成wget命令"
echo "   如果您有HTTP服务器，可以："
echo "   1. 将文件放到HTTP服务器"
echo "   2. 在目标服务器执行："
echo "   wget http://your-server.com/custom.js -O /var/www/html/custom.js"
echo ""

echo "🎯 推荐使用方案3（修复SSH密钥）获得最佳体验"

# 生成文件信息
echo ""
echo "📄 文件信息："
echo "   文件名: custom.js"
echo "   大小: $(ls -lh dist/custom.js | awk '{print $5}')"
echo "   MD5: $(md5 -q dist/custom.js)"
echo "   目标服务器: *************"
echo ""

echo "🔗 部署后的使用方法："
echo "   在TCIC课堂URL后添加："
echo "   &debugjs=http://*************/custom.js"
echo "   或"
echo "   &debugjs=https://your-domain.com/custom.js"
