# 白板工具栏插件

这是一个为TCIC互动白板系统开发的工具栏插件，提供了完整的白板操作功能。

## 功能特性

### 🎯 15个核心工具（按要求顺序排列）
1. **鼠标** - 选择和移动工具
2. **画笔** - 自由绘制
3. **形状** - 绘制几何形状
4. **橡皮** - 擦除内容
5. **框选** - 选择区域
6. **文本** - 添加文本
7. **激光笔** - 激光指示
8. **截图** - 屏幕截图
9. **绘图工具** - 高级绘图
10. **自定义图形** - 自定义形状
11. **调色盘** - 颜色选择
12. **学科公式** - 数学公式编辑器
13. **撤销** - 撤销操作
14. **恢复** - 恢复操作
15. **收起** - 收起/展开工具栏

### 🧮 学科公式编辑器
- **7个学科分类**：基础数学、代数、几何、三角函数、微积分、物理、化学
- **56个预设公式模板**
- **LaTeX代码编辑**
- **位置控制系统**：
  - 精确位置输入（水平/垂直百分比）
  - 9个快速位置选择（左上、中上、右上等）
- **样式控制**：字体大小、颜色自定义
- **实时预览**
- **防重叠插入**

### 🎨 调色盘功能
- 12种预设颜色
- 快速颜色切换
- 悬浮式颜色选择器

### 📱 响应式设计
- 固定位置浮动工具栏
- 收起/展开功能
- 移动端友好的按钮尺寸

## 技术架构

### 前端技术栈
- **Vue 3** - 组合式API
- **自定义元素** - Web Components标准
- **Rspack** - 现代化构建工具
- **CSS Grid/Flexbox** - 响应式布局

### 集成方式
- **插件化架构** - 作为自定义元素集成
- **无侵入式** - 不影响现有系统
- **TEduBoard SDK** - 深度集成腾讯互动白板

## 文件结构

```
src/
├── components/
│   ├── WhiteboardToolbar.vue      # 主工具栏组件
│   └── FormulaEditorModal.vue     # 公式编辑器模态框
├── WhiteboardToolbar.ce.vue       # 自定义元素包装器
└── main.js                        # 入口文件和组件注册
```

## 使用方法

### 1. 构建插件
```bash
npm run build
```

### 2. 部署文件
构建后的文件位于 `dist/` 目录，包含：
- `main.js` - 主要逻辑
- `main.css` - 样式文件

### 3. 集成到项目
在HTML页面中引入构建后的文件：
```html
<script src="path/to/dist/main.js"></script>
<link rel="stylesheet" href="path/to/dist/main.css">
```

### 4. 自动加载
插件会自动检测用户角色：
- **教师用户**：显示完整工具栏
- **学生用户**：不显示工具栏（可根据需要调整）

## API接口

### TEduBoard SDK集成
工具栏直接调用TEduBoard SDK的以下方法：
- `setToolType()` - 设置工具类型
- `setBrushColor()` - 设置画笔颜色
- `undo()` / `redo()` - 撤销/恢复
- `snapshot()` - 截图
- `addElement()` - 添加公式元素

### 自定义元素
```javascript
// 手动创建工具栏
const toolbar = document.createElement('whiteboard-toolbar');
document.body.appendChild(toolbar);
```

## 配置选项

### 工具栏位置
默认位置：左侧居中，可通过CSS调整：
```css
.whiteboard-toolbar {
  left: 10px;  /* 距离左边距离 */
  top: 50%;    /* 垂直居中 */
}
```

### 公式编辑器
默认配置可在 `FormulaEditorModal.vue` 中修改：
- 默认位置：(10%, 10%)
- 默认字体大小：16px
- 默认颜色：黑色

## 兼容性

- **浏览器支持**：Chrome 54+, Firefox 63+, Safari 10.1+
- **Vue版本**：Vue 3.x
- **TEduBoard SDK**：兼容最新版本

## 开发说明

### 本地开发
```bash
npm run dev
```

### 添加新工具
1. 在 `WhiteboardToolbar.vue` 中添加按钮
2. 实现对应的处理方法
3. 调用相应的TEduBoard SDK方法

### 添加新公式分类
在 `FormulaEditorModal.vue` 的 `formulaCategories` 数组中添加新分类。

## 部署建议

### 生产环境
1. 使用CDN加速静态资源
2. 启用gzip压缩
3. 设置合适的缓存策略

### 版本管理
建议使用语义化版本号，当前版本：1.0.0

## 更新日志

### v1.0.0 (2024-09-12)
- ✅ 完成15个核心工具的实现
- ✅ 学科公式编辑器功能完整
- ✅ 位置控制和样式自定义
- ✅ 响应式设计和收起/展开功能
- ✅ Vue 3 + 自定义元素架构
- ✅ TEduBoard SDK深度集成

## 技术支持

如有问题或建议，请联系开发团队。

---

**注意**：此工具栏专为TCIC互动白板系统设计，需要TEduBoard SDK支持才能正常工作。
