/**
 * 课前提问工具权限配置示例
 * 参考腾讯云TCIC白板权限配置文档：https://cloud.tencent.com/document/product/1639/114534
 * 
 * 使用方法：
 * 1. 将此文件内容添加到您的自定义脚本中
 * 2. 根据需要调整权限配置
 * 3. 在TCIC课堂中测试功能
 */

// 等待课堂状态就绪后配置权限
TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
    console.log('课堂已开始，开始配置课前提问工具权限');
    
    const isStudent = TCIC.SDK.instance.isStudent();
    const isTeacher = TCIC.SDK.instance.isTeacher();
    
    // 配置示例1：完全禁用学生的问题查看功能
    if (isStudent) {
        console.log('学生用户：禁用所有问题查看功能');
        // 注意：实际的权限控制在组件内部已经实现
        // 这里只是演示如何进行额外的配置
    }
    
    // 配置示例2：限制老师的某些功能（特殊场景）
    if (isTeacher) {
        console.log('老师用户：配置问题查看权限');
        
        // 获取问题组件实例（如果需要动态配置）
        setTimeout(() => {
            const questionModal = document.querySelector('question-modal');
            if (questionModal && questionModal.__vueParentComponent) {
                const vueInstance = questionModal.__vueParentComponent.ctx;
                
                // 示例：禁用键盘控制（仅允许鼠标操作）
                if (vueInstance.setQuestionFeatureAvailable) {
                    vueInstance.setQuestionFeatureAvailable('allowKeyboardControl', false);
                    console.log('已禁用键盘控制功能');
                }
                
                // 示例：禁用触摸控制（仅桌面端使用）
                if (vueInstance.setQuestionFeatureAvailable) {
                    vueInstance.setQuestionFeatureAvailable('allowTouchControl', false);
                    console.log('已禁用触摸控制功能');
                }
            }
        }, 1000);
    }
});

// 监听权限状态变化（参考白板权限配置）
TCIC.SDK.instance.subscribeState && TCIC.SDK.instance.subscribeState(TCIC.TMainState.Board_Permission, async (value) => {
    console.log('权限状态变化:', value);
    
    const isStudent = TCIC.SDK.instance.isStudent();
    if (isStudent && value) {
        // 当学生获得权限时的处理
        console.log('学生获得权限，但问题查看功能仍然受限');
    }
});

// 配置示例3：根据课堂类型动态配置权限
function configureQuestionPermissionsByClassType() {
    // 获取课堂信息（如果有相关API）
    const classInfo = TCIC.SDK.instance.getClassInfo && TCIC.SDK.instance.getClassInfo();
    
    if (classInfo) {
        console.log('课堂信息:', classInfo);
        
        // 根据课堂类型配置不同的权限
        switch (classInfo.classType) {
            case 'demo':
                // 演示课堂：允许所有功能
                console.log('演示课堂：启用所有问题查看功能');
                break;
                
            case 'exam':
                // 考试课堂：限制功能
                console.log('考试课堂：限制问题查看功能');
                break;
                
            default:
                // 普通课堂：默认配置
                console.log('普通课堂：使用默认权限配置');
                break;
        }
    }
}

// 配置示例4：基于时间的权限控制
function configureTimeBasedPermissions() {
    const now = new Date();
    const hour = now.getHours();
    
    // 例如：晚上8点后限制某些功能
    if (hour >= 20) {
        console.log('晚间时段：应用限制性权限配置');
        // 可以在这里调用相应的权限配置函数
    }
}

// 配置示例5：基于用户角色的细粒度控制
function configureRoleBasedPermissions() {
    const userRole = TCIC.SDK.instance.getUserRole && TCIC.SDK.instance.getUserRole();
    
    switch (userRole) {
        case 'teacher':
            console.log('主讲老师：完全权限');
            break;
            
        case 'assistant':
            console.log('助教：部分权限');
            // 可以限制某些功能
            break;
            
        case 'observer':
            console.log('观察员：只读权限');
            // 可以禁用大部分交互功能
            break;
            
        default:
            console.log('默认角色权限');
            break;
    }
}

// 工具函数：批量配置权限
function batchConfigureQuestionPermissions(permissions) {
    setTimeout(() => {
        const questionModal = document.querySelector('question-modal');
        if (questionModal && questionModal.__vueParentComponent) {
            const vueInstance = questionModal.__vueParentComponent.ctx;
            
            if (vueInstance.setQuestionFeatureAvailable) {
                Object.keys(permissions).forEach(feature => {
                    vueInstance.setQuestionFeatureAvailable(feature, permissions[feature]);
                    console.log(`配置 ${feature}:`, permissions[feature]);
                });
            }
        }
    }, 1000);
}

// 使用示例：
// 严格模式 - 仅允许查看，禁用所有交互
function enableStrictMode() {
    batchConfigureQuestionPermissions({
        allowQuestionView: true,
        allowQuestionRefresh: false,
        allowQuestionNavigation: false,
        allowKeyboardControl: false,
        allowTouchControl: false
    });
}

// 演示模式 - 启用所有功能
function enableDemoMode() {
    batchConfigureQuestionPermissions({
        allowQuestionView: true,
        allowQuestionRefresh: true,
        allowQuestionNavigation: true,
        allowKeyboardControl: true,
        allowTouchControl: true
    });
}

// 移动端模式 - 优化移动端体验
function enableMobileMode() {
    batchConfigureQuestionPermissions({
        allowQuestionView: true,
        allowQuestionRefresh: true,
        allowQuestionNavigation: true,
        allowKeyboardControl: false,  // 移动端通常没有键盘
        allowTouchControl: true       // 启用触摸控制
    });
}

// 根据设备类型自动配置
function autoConfigureByDevice() {
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
        console.log('检测到移动设备，应用移动端配置');
        enableMobileMode();
    } else {
        console.log('检测到桌面设备，应用默认配置');
        enableDemoMode();
    }
}

// 初始化权限配置
console.log('课前提问工具权限配置脚本已加载');

// 可以根据需要调用相应的配置函数
// autoConfigureByDevice();
// configureQuestionPermissionsByClassType();
// configureTimeBasedPermissions();
// configureRoleBasedPermissions();
