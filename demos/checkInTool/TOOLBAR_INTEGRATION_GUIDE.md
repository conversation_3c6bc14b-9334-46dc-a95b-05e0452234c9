# 白板工具栏插件集成指南

## 权限配置

### 当前权限设置
- **教师用户**：
  - ✅ 白板工具栏（完整15个工具）
  - ✅ 问题显示组件
  - ✅ 签到按钮

- **学生用户**：
  - ✅ 白板工具栏（完整15个工具）
  - ✅ 学生模态框

### 核心功能
1. **白板工具栏**：师生都可见，包含15个工具按要求顺序排列
2. **学科公式编辑器**：师生都可使用，包含7个学科分类，56个公式模板
3. **位置控制系统**：防止公式重叠，支持精确位置设置

## 构建和部署

### 构建命令
```bash
npm run build
```

### 构建产物
- `dist/custom.js` - 主要逻辑文件
- `dist/custom.js.map` - 源码映射文件

### 集成方式
```html
<script src="path/to/dist/custom.js"></script>
```

## 技术特点

### Vue 3 + 自定义元素
- 使用Vue 3组合式API
- 基于Web Components标准
- 无侵入式集成

### TEduBoard SDK集成
- 直接调用TEduBoard API
- 支持所有白板操作
- 公式插入功能完整

### 响应式设计
- 固定位置浮动工具栏
- 收起/展开功能
- 移动端友好

## 版本信息
- 版本：v1.0.0
- 构建时间：2024-09-12
- Vue版本：3.x
- 构建工具：Rspack

## 使用说明

插件会在页面加载完成后自动初始化：
1. 检测用户角色（教师/学生）
2. 根据权限显示相应组件
3. 白板工具栏对所有用户可见
4. 问题显示组件仅教师可见

工具栏包含完整的白板操作功能，学科公式编辑器支持LaTeX格式，具备位置控制和样式自定义功能。
