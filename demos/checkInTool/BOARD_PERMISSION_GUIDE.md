# 🎨 白板权限管理功能使用指南

## 📋 功能概述

基于腾讯云TCIC白板权限配置文档，我们为您的课前提问工具添加了完整的白板权限管理功能。老师可以通过直观的界面管理学生的白板使用权限。

## 🎯 主要功能

### 1. **权限管理界面**
- 📊 **实时成员列表**: 显示所有在线学生
- 🔍 **权限状态查看**: 清晰显示每个学生的白板权限状态
- ⚡ **一键操作**: 单击即可授权或收回权限
- 📱 **响应式设计**: 支持桌面和移动设备

### 2. **批量操作**
- ✅ **全部授权**: 一键给所有学生开启白板权限
- ❌ **全部收回**: 一键收回所有学生的白板权限
- 🔄 **实时刷新**: 自动同步最新的成员状态

### 3. **智能权限控制**
- 👨‍🏫 **老师专用**: 只有老师才能看到和使用权限管理功能
- 🔒 **安全验证**: 每次操作都会验证用户身份
- 📝 **操作日志**: 详细记录所有权限变更操作

## 🚀 使用方法

### 步骤1：打开权限管理
1. 老师在TCIC课堂中点击工具栏的 **🎨 白板** 按钮
2. 系统会弹出白板权限管理界面

### 步骤2：查看学生状态
- **绿色标签**: 已授权 - 学生可以使用白板
- **红色标签**: 未授权 - 学生无法使用白板
- **成员信息**: 显示学生姓名和在线状态

### 步骤3：管理权限
#### 单个学生操作
- 点击 **授权** 按钮：给学生开启白板权限
- 点击 **收回** 按钮：收回学生的白板权限

#### 批量操作
- 点击 **✅ 全部授权**：给所有学生开启白板权限
- 点击 **❌ 全部收回**：收回所有学生的白板权限

### 步骤4：实时监控
- 界面底部显示统计信息：
  - **在线学生**: 当前在线的学生总数
  - **已授权**: 已获得白板权限的学生数量

## 🔧 技术实现

### TCIC SDK集成
```javascript
// 授权白板权限
TCIC.SDK.instance.setBoardPermission(userId, true);

// 收回白板权限
TCIC.SDK.instance.setBoardPermission(userId, false);

// 检查权限状态
const teduBoard = TCIC.SDK.instance.getBoard();
const permission = teduBoard.getUserPermission(userId);
```

### 权限配置选项
```javascript
// 详细权限配置
teduBoard.setPermission(userId, {
  canDraw: true,        // 允许涂鸦
  canOperate: true,     // 允许操作
  canAddPage: false,    // 不允许添加页面
  canDeletePage: false  // 不允许删除页面
});
```

## 📱 界面预览

### 桌面端界面
```
┌─────────────────────────────────────────┐
│ 🎨 白板权限管理              [刷新] [关闭] │
├─────────────────────────────────────────┤
│ [✅ 全部授权] [❌ 全部收回]              │
├─────────────────────────────────────────┤
│ 👤 张三    [学生] [已授权]     [收回]    │
│ 👤 李四    [学生] [未授权]     [授权]    │
│ 👤 王五    [学生] [已授权]     [收回]    │
├─────────────────────────────────────────┤
│ 在线学生: 3 人          已授权: 2 人     │
└─────────────────────────────────────────┘
```

### 移动端界面
- 自适应布局，优化触摸操作
- 简化显示信息，保持核心功能
- 支持手势操作

## 🎓 使用场景

### 1. **课堂互动**
- **场景**: 老师邀请学生上台演示
- **操作**: 给特定学生开启白板权限
- **效果**: 学生可以在白板上书写和绘图

### 2. **小组讨论**
- **场景**: 分组讨论，每组派代表展示
- **操作**: 批量给小组代表授权
- **效果**: 多个学生可以同时使用白板

### 3. **考试模式**
- **场景**: 在线考试或测验
- **操作**: 收回所有学生的白板权限
- **效果**: 防止学生在考试中使用白板作弊

### 4. **自由练习**
- **场景**: 课堂练习时间
- **操作**: 全部授权给所有学生
- **效果**: 所有学生都可以自由使用白板

## ⚠️ 注意事项

### 1. **权限验证**
- 只有老师角色才能使用白板权限管理功能
- 学生无法看到权限管理界面

### 2. **网络状态**
- 权限变更需要网络连接
- 建议在网络稳定的环境下操作

### 3. **实时同步**
- 权限变更会立即生效
- 学生端会实时收到权限状态更新

### 4. **兼容性**
- 支持TCIC SDK 2.0及以上版本
- 兼容主流浏览器（Chrome、Firefox、Safari、Edge）

## 🔍 故障排除

### 问题1：权限管理按钮不显示
**原因**: 当前用户不是老师角色
**解决**: 确认使用老师账号登录TCIC课堂

### 问题2：成员列表为空
**原因**: 课堂中暂无学生，或网络连接问题
**解决**: 
1. 确认有学生加入课堂
2. 点击刷新按钮重新加载
3. 检查网络连接状态

### 问题3：权限设置不生效
**原因**: TCIC SDK版本过低或API调用失败
**解决**:
1. 检查TCIC SDK版本
2. 查看浏览器控制台错误信息
3. 重新尝试操作

### 问题4：界面显示异常
**原因**: 浏览器兼容性问题
**解决**:
1. 使用推荐的浏览器版本
2. 清除浏览器缓存
3. 刷新页面重新加载

## 📞 技术支持

### 开发者调试
```javascript
// 开启调试模式
localStorage.setItem('tcic-debug', 'true');

// 查看权限状态
console.log('当前用户角色:', TCIC.SDK.instance.isTeacher() ? '老师' : '学生');

// 查看成员列表
console.log('成员列表:', TCIC.SDK.instance.getMemberList());
```

### 日志查看
- 打开浏览器开发者工具
- 查看Console标签页
- 搜索关键词："白板权限"、"board permission"

### 联系支持
如果遇到技术问题，请提供：
1. 浏览器版本信息
2. TCIC SDK版本
3. 错误日志截图
4. 操作步骤描述

---

**🎉 现在您可以轻松管理课堂中的白板权限，让教学更加高效！**
