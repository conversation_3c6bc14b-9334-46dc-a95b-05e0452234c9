<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原生工具栏公式按钮测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        /* 模拟TCIC原生工具栏样式 */
        .mock-native-toolbar {
            background: #2c3e50;
            padding: 10px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .board-tools-component-menu.expand {
            display: block;
        }
        .board-tools-component-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            gap: 5px;
        }
        .board-tools-component-menu li {
            margin: 0;
            padding: 0;
        }
        .board-tools-component-menu button {
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }
        .board-tools-component-menu button:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* 添加原生工具栏的active状态样式 */
        .board-tools-component-menu ul li.active,
        .board-tools-component-menu ul li:active {
            transition: all .6s;
            background: #006eff;
            padding-left: 7px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 原生工具栏公式按钮测试</h1>
            <p>测试在TCIC原生白板工具栏中添加公式按钮</p>
        </div>

        <div class="content">
            <div id="status" class="status info">
                <strong>状态：</strong>正在初始化...
            </div>

            <div class="demo-section">
                <h3>🎯 测试功能</h3>
                <button class="btn" onclick="loadCustomScript()">🔄 加载公式按钮组件</button>
                <button class="btn" onclick="testFormulaButton()">🧮 测试公式按钮</button>
                <button class="btn" onclick="simulateNativeToolbar()">🛠️ 模拟原生工具栏</button>
            </div>

            <div class="demo-section">
                <h3>🛠️ 模拟TCIC原生白板工具栏</h3>
                <div class="mock-native-toolbar">
                    <div class="board-tools-component-menu expand">
                        <ul id="mockToolbarUl">
                            <!-- 模拟原生工具按钮 -->
                            <li onclick="setActiveTool(this)">
                                <button class="el-tooltip" title="鼠标">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M4.58 2.74a.952.952 0 011.427-.675l.12.087 8.858 7.353.086.077c.813.79.327 2.189-.801 2.304l-.114.008-3.776.09a1.251 1.251 0 00-.825.338l-.091.095-2.468 2.859c-.815.944-2.366.371-2.372-.877L4.57 2.887l.01-.148z" stroke="var(--icon-color, #fff)" stroke-width="1.5"></path>
                                    </svg>
                                </button>
                            </li>
                            <li onclick="setActiveTool(this)">
                                <button class="el-tooltip" title="画笔">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M2 16l4-4 8-8 2-2-2-2-2 2-8 8-4 4 2 2z" stroke="var(--icon-color, #fff)" stroke-width="1.5"></path>
                                    </svg>
                                </button>
                            </li>
                            <li>
                                <button class="el-tooltip" title="橡皮">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 3l12 12M3 15l12-12" stroke="var(--icon-color, #fff)" stroke-width="1.5"></path>
                                    </svg>
                                </button>
                            </li>
                            <li>
                                <button class="el-tooltip" title="文本">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 3h12M9 3v12M6 15h6" stroke="var(--icon-color, #fff)" stroke-width="1.5"></path>
                                    </svg>
                                </button>
                            </li>
                            <li>
                                <button class="el-tooltip" title="撤销">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 9a6 6 0 016-6h6M3 9l3-3M3 9l3 3" stroke="var(--icon-color, #fff)" stroke-width="1.5"></path>
                                    </svg>
                                </button>
                            </li>
                            <li>
                                <button class="el-tooltip" title="重做">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M15 9a6 6 0 01-6-6H3M15 9l-3-3M15 9l-3 3" stroke="var(--icon-color, #fff)" stroke-width="1.5"></path>
                                    </svg>
                                </button>
                            </li>
                            <!-- 公式按钮将被插入到倒数第三个位置（撤销按钮之前） -->
                        </ul>
                    </div>
                </div>
                <p><small>👆 公式按钮将会被插入到倒数第三个位置（撤销按钮之前）</small></p>
            </div>

            <div class="demo-section">
                <h3>📋 功能说明</h3>
                <ul>
                    <li>✅ <strong>简化架构</strong>：移除了自定义白板工具栏</li>
                    <li>✅ <strong>原生集成</strong>：直接在TCIC原生工具栏中添加公式按钮</li>
                    <li>✅ <strong>保留弹窗</strong>：公式编辑器弹窗功能完整保留</li>
                    <li>✅ <strong>样式一致</strong>：按钮样式与原生工具栏完全一致</li>
                    <li>✅ <strong>事件驱动</strong>：使用全局事件系统管理状态</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 模拟TCIC SDK环境
        window.TCIC = {
            SDK: {
                instance: {
                    isTeacher: () => true,
                    isStudent: () => false,
                    getUserId: () => 'teacher_demo_123',
                    getClassInfo: () => ({ classId: '245783322' }),
                    promiseState: (state, value) => {
                        console.log('TCIC promiseState:', state, value);
                        return Promise.resolve();
                    },
                    on: (event, callback) => {
                        console.log('TCIC event listener added:', event);
                    },
                    updateTask: (taskId, content) => {
                        console.log('TCIC Task updated:', taskId, content);
                        updateStatus('success', `任务更新: ${taskId}`);
                    }
                }
            }
        };

        // 模拟白板API
        window.teduBoard = {
            addElement: (type, data, options) => {
                console.log('模拟插入公式到白板:', { type, data, options });
                updateStatus('success', `公式已插入: ${data.expression}`);
            }
        };

        window.TEduBoard = {
            TEduBoardElementType: {
                TEDU_BOARD_ELEMENT_FORMULA: 'formula'
            }
        };

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.innerHTML = `<strong>状态：</strong>${message}`;
        }

        function simulateNativeToolbar() {
            updateStatus('info', '模拟原生工具栏环境已准备就绪');
        }

        function setActiveTool(toolElement) {
            // 移除所有工具的active状态
            const allTools = document.querySelectorAll('.board-tools-component-menu ul li');
            allTools.forEach(tool => tool.classList.remove('active'));

            // 设置当前工具为active
            toolElement.classList.add('active');

            const toolName = toolElement.querySelector('button').getAttribute('title');
            updateStatus('info', `已选择工具: ${toolName}`);
        }

        function testFormulaButton() {
            const formulaBtn = document.querySelector('.custom-formula-btn button');
            if (formulaBtn) {
                formulaBtn.click();
                updateStatus('success', '已点击公式按钮，观察active状态效果');
            } else {
                updateStatus('error', '公式按钮未找到，请先加载组件');
            }
        }

        function loadCustomScript() {
            updateStatus('info', '正在加载公式按钮组件...');

            // 移除已存在的脚本
            const existingScript = document.querySelector('script[src*="custom.js"]');
            if (existingScript) {
                existingScript.remove();
            }

            // 加载新脚本
            const script = document.createElement('script');
            script.src = 'http://localhost:3001/custom.js?' + Date.now();
            script.onload = () => {
                updateStatus('success', '公式按钮组件加载成功！');
                
                // 等待一下让组件初始化
                setTimeout(() => {
                    const formulaBtn = document.querySelector('.custom-formula-btn');
                    if (formulaBtn) {
                        updateStatus('success', '公式按钮已添加到原生工具栏');
                    }
                }, 1000);
            };
            script.onerror = () => {
                updateStatus('error', '无法加载组件，请确保开发服务器正在运行');
            };
            document.head.appendChild(script);
        }

        // 监听公式编辑器事件
        document.addEventListener('show-formula-editor', (event) => {
            updateStatus('info', `公式编辑器被 ${event.detail.source} 触发显示`);
        });

        // 页面加载完成后自动模拟环境
        window.addEventListener('load', () => {
            simulateNativeToolbar();
            setTimeout(() => {
                updateStatus('info', '环境准备就绪，点击"加载公式按钮组件"开始测试');
            }, 500);
        });
    </script>
</body>
</html>
