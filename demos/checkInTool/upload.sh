#!/bin/bash

# 课前提问工具上传脚本
# 使用方法: ./upload.sh username@server-ip /remote/path

echo "🚀 课前提问工具上传脚本"
echo "================================"

# 检查参数
if [ $# -lt 2 ]; then
    echo "❌ 使用方法: $0 username@server-ip /remote/path"
    echo "例如: $0 root@************* /var/www/html/tcic/"
    exit 1
fi

SERVER=$1
REMOTE_PATH=$2

echo "📦 检查构建文件..."
if [ ! -f "dist/custom.js" ]; then
    echo "❌ 构建文件不存在，正在构建..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ 构建失败"
        exit 1
    fi
fi

echo "✅ 构建文件存在: $(ls -lh dist/custom.js | awk '{print $5}')"

echo "🔑 测试SSH连接..."
ssh -o ConnectTimeout=10 -o BatchMode=yes $SERVER "echo '✅ SSH连接成功'"
if [ $? -ne 0 ]; then
    echo "❌ SSH连接失败，请检查："
    echo "   1. 服务器地址和用户名是否正确"
    echo "   2. SSH公钥是否已添加到服务器"
    echo "   3. 服务器SSH服务是否正常"
    echo ""
    echo "🔧 添加公钥到服务器的命令："
    echo "   ssh-copy-id $SERVER"
    echo "   或手动添加公钥："
    echo "   cat ~/.ssh/id_ed25519.pub"
    exit 1
fi

echo "📤 上传文件到服务器..."
echo "   源文件: dist/custom.js"
echo "   目标: $SERVER:$REMOTE_PATH"

# 创建远程目录
ssh $SERVER "mkdir -p $REMOTE_PATH"

# 上传文件
scp dist/custom.js $SERVER:$REMOTE_PATH/
if [ $? -eq 0 ]; then
    echo "✅ 文件上传成功！"
    
    # 检查远程文件
    echo "📋 远程文件信息："
    ssh $SERVER "ls -lh $REMOTE_PATH/custom.js"
    
    echo ""
    echo "🌐 使用方法："
    echo "   在TCIC课堂URL后添加："
    echo "   &debugjs=https://your-domain.com$REMOTE_PATH/custom.js"
    echo ""
    echo "🔧 如果使用IP地址，请确保配置CORS："
    echo "   Access-Control-Allow-Origin: *"
    
else
    echo "❌ 文件上传失败"
    exit 1
fi

echo ""
echo "🎉 部署完成！"
