/* 原生白板工具栏中的自定义公式按钮样式 */
.board-tools-component-menu .custom-formula-btn {
  /* 继承原生工具栏按钮的样式 */
  margin: 0;
  padding: 0;
  list-style: none;
}

.board-tools-component-menu .custom-formula-btn button {
  /* 确保按钮样式与原生按钮一致 */
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.board-tools-component-menu ul li.custom-formula-btn:hover {
  margin-left: 7px;
}

.board-tools-component-menu ul li.custom-formula-btn:hover button {
  background-color: transparent;
  padding-right: 12px;
}

/* 确保SVG图标样式正确 */
.board-tools-component-menu .custom-formula-btn svg {
  width: 18px;
  height: 18px;
  pointer-events: none;
}

/* 工具提示样式 */
.board-tools-component-menu ul li.custom-formula-btn button::after {
  content: attr(title);
  position: absolute;
  left: 50%;
  bottom: -30px;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  z-index: 1000;
}

.board-tools-component-menu ul li.custom-formula-btn:hover button::after {
  opacity: 1;
}

/* 确保在不同主题下都能正常显示 */
.board-tools-component-menu.dark .custom-formula-btn button {
  color: #fff;
}

.board-tools-component-menu.light .custom-formula-btn button {
  color: #333;
}

/* 选中状态样式 - 与原生工具栏保持一致 */
.board-tools-component-menu ul li.custom-formula-btn.active,
.board-tools-component-menu ul li.custom-formula-btn:active {
  /* transition: all .6s; */
  background: #006eff;
  margin-left: 7px;
}

.board-tools-component-menu .custom-formula-btn.active button,
.board-tools-component-menu .custom-formula-btn:active button {
  background-color: transparent; /* 重置按钮背景，使用li的背景 */
}

.board-tools-component-menu .custom-formula-btn.active button svg,
.board-tools-component-menu .custom-formula-btn:active button svg {
  filter: brightness(1.2);
}
