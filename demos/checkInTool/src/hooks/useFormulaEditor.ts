import { ref, reactive } from 'vue';

// 全局状态管理
const isFormulaEditorVisible = ref(false);
const formulaEditorData = reactive({
  source: '', // 触发来源：'toolbar' | 'teacher-btn'
  callback: null // 插入回调函数
});

export function useFormulaEditor() {
  const showFormulaEditor = (source = 'unknown', callback = null) => {
    formulaEditorData.source = source;
    formulaEditorData.callback = callback;
    isFormulaEditorVisible.value = true;
    
    console.log(`公式编辑器被 ${source} 触发显示`);
  };

  const hideFormulaEditor = () => {
    isFormulaEditorVisible.value = false;
    formulaEditorData.source = '';
    formulaEditorData.callback = null;
    
    console.log('公式编辑器已隐藏');
  };

  const insertFormula = (formulaData) => {
    if (formulaEditorData.callback && typeof formulaEditorData.callback === 'function') {
      formulaEditorData.callback(formulaData);
    } else {
      // 默认处理：插入到白板
      if (window.teduBoard && window.TEduBoard) {
        try {
          window.teduBoard.addElement(
            window.TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_FORMULA,
            { expression: formulaData.latex },
            {
              erasable: true,
              left: `${formulaData.position.left}%`,
              top: `${formulaData.position.top}%`,
              fontSize: `${formulaData.style.fontSize}px`,
              fontColor: formulaData.style.fontColor + 'FF',
            }
          );
          console.log('公式已插入到白板:', formulaData);
        } catch (error) {
          console.error('插入公式失败:', error);
        }
      } else {
        console.warn('白板API不可用，无法插入公式');
      }
    }
    
    hideFormulaEditor();
  };

  return {
    isFormulaEditorVisible,
    formulaEditorData,
    showFormulaEditor,
    hideFormulaEditor,
    insertFormula
  };
}

// 全局事件监听
if (typeof window !== 'undefined') {
  const { showFormulaEditor } = useFormulaEditor();
  
  // 监听自定义事件
  document.addEventListener('show-formula-editor', (event) => {
    const { source, callback } = event.detail || {};
    showFormulaEditor(source, callback);
  });
}
