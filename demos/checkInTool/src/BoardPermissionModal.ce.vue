<template>
  <div v-if="showModal" class="board-permission-modal">
    <div class="board-permission-modal__content">
      <div class="board-permission-modal__header">
        <h2>🎨 白板权限管理</h2>
        <div class="board-permission-modal__controls">
          <button @click="refreshMemberList" class="refresh-btn">刷新</button>
          <button @click="showModal = false" class="close-btn">关闭</button>
        </div>
      </div>

      <div class="board-permission-modal__body">
        <div v-if="loading" class="loading">
          <div class="loading-spinner"></div>
          <p>加载成员列表中...</p>
        </div>
        
        <div v-else-if="members.length === 0" class="no-members">
          <div class="empty-icon">👥</div>
          <p>暂无在线成员</p>
        </div>
        
        <div v-else class="members-list">
          <div class="batch-controls">
            <button @click="grantAllStudents" class="batch-btn grant-all">
              ✅ 全部授权
            </button>
            <button @click="revokeAllStudents" class="batch-btn revoke-all">
              ❌ 全部收回
            </button>
          </div>
          
          <div class="member-item" v-for="member in students" :key="member.userId">
            <div class="member-info">
              <div class="member-avatar">
                <img v-if="member.avatar" :src="member.avatar" :alt="member.name" />
                <div v-else class="default-avatar">{{ member.name?.charAt(0) || '?' }}</div>
              </div>
              <div class="member-details">
                <div class="member-name">{{ member.name || member.userId }}</div>
                <div class="member-status">
                  <span class="role-tag">学生</span>
                  <span :class="['permission-status', member.hasBoardPermission ? 'granted' : 'denied']">
                    {{ member.hasBoardPermission ? '已授权' : '未授权' }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="member-actions">
              <button 
                v-if="!member.hasBoardPermission"
                @click="grantPermission(member.userId)"
                class="action-btn grant-btn"
              >
                授权
              </button>
              <button 
                v-else
                @click="revokePermission(member.userId)"
                class="action-btn revoke-btn"
              >
                收回
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="board-permission-modal__footer">
        <div class="footer-info">
          <span class="member-count">在线学生: {{ students.length }} 人</span>
          <span class="permission-count">已授权: {{ authorizedCount }} 人</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 响应式数据
const showModal = ref(false);
const loading = ref(false);
const members = ref([]);

// 计算属性
const students = computed(() => {
  return members.value.filter(member => member.role === 'student');
});

const authorizedCount = computed(() => {
  return students.value.filter(member => member.hasBoardPermission).length;
});

// Task系统（简化版，用于Custom Element）
const useTask = (taskName) => {
  const updateTask = (data) => {
    const event = new CustomEvent('tcic-task-update', {
      detail: { taskName, data }
    });
    document.dispatchEvent(event);
  };

  const handleTaskUpdate = (event) => {
    if (event.detail.taskName === taskName) {
      // 处理任务更新
    }
  };

  onMounted(() => {
    document.addEventListener('tcic-task-update', handleTaskUpdate);
  });

  onUnmounted(() => {
    document.removeEventListener('tcic-task-update', handleTaskUpdate);
  });

  return { updateTask };
};

// 获取成员列表
const fetchMemberList = async () => {
  loading.value = true;
  try {
    // 模拟成员数据（实际应该从TCIC SDK获取）
    const mockMembers = [
      {
        userId: 'student1',
        name: '张三',
        role: 'student',
        avatar: null,
        hasBoardPermission: false
      },
      {
        userId: 'student2', 
        name: '李四',
        role: 'student',
        avatar: null,
        hasBoardPermission: true
      },
      {
        userId: 'teacher1',
        name: '王老师',
        role: 'teacher',
        avatar: null,
        hasBoardPermission: true
      }
    ];

    // 如果TCIC SDK可用，使用真实数据
    if (window.TCIC && TCIC.SDK.instance.getMemberList) {
      const memberList = TCIC.SDK.instance.getMemberList();
      const membersWithPermission = await Promise.all(
        memberList.map(async (member) => {
          let hasBoardPermission = false;
          try {
            hasBoardPermission = await checkBoardPermission(member.userId);
          } catch (error) {
            console.warn(`检查用户 ${member.userId} 白板权限失败:`, error);
          }
          return { ...member, hasBoardPermission };
        })
      );
      members.value = membersWithPermission;
    } else {
      // 使用模拟数据
      members.value = mockMembers;
    }
    
    console.log('成员列表获取成功:', members.value);
  } catch (error) {
    console.error('获取成员列表失败:', error);
    members.value = [];
  } finally {
    loading.value = false;
  }
};

// 检查用户白板权限
const checkBoardPermission = async (userId) => {
  try {
    if (window.TCIC && TCIC.SDK.instance.getBoard) {
      const teduBoard = TCIC.SDK.instance.getBoard();
      if (teduBoard && teduBoard.getUserPermission) {
        const permission = teduBoard.getUserPermission(userId);
        return permission && permission.canDraw;
      }
    }
    return false;
  } catch (error) {
    console.error('检查白板权限失败:', error);
    return false;
  }
};

// 授权白板权限
const grantPermission = async (userId) => {
  try {
    console.log(`授权用户 ${userId} 白板权限`);
    
    if (window.TCIC && TCIC.SDK.instance.setBoardPermission) {
      await TCIC.SDK.instance.setBoardPermission(userId, true);
    }
    
    // 更新本地状态
    const member = members.value.find(m => m.userId === userId);
    if (member) {
      member.hasBoardPermission = true;
    }
    
    console.log(`用户 ${userId} 白板权限授权成功`);
  } catch (error) {
    console.error(`授权用户 ${userId} 白板权限失败:`, error);
  }
};

// 收回白板权限
const revokePermission = async (userId) => {
  try {
    console.log(`收回用户 ${userId} 白板权限`);
    
    if (window.TCIC && TCIC.SDK.instance.setBoardPermission) {
      await TCIC.SDK.instance.setBoardPermission(userId, false);
    }
    
    // 更新本地状态
    const member = members.value.find(m => m.userId === userId);
    if (member) {
      member.hasBoardPermission = false;
    }
    
    console.log(`用户 ${userId} 白板权限收回成功`);
  } catch (error) {
    console.error(`收回用户 ${userId} 白板权限失败:`, error);
  }
};

// 批量授权所有学生
const grantAllStudents = async () => {
  console.log('批量授权所有学生白板权限');
  const studentList = students.value.filter(s => !s.hasBoardPermission);
  
  for (const student of studentList) {
    await grantPermission(student.userId);
  }
};

// 批量收回所有学生权限
const revokeAllStudents = async () => {
  console.log('批量收回所有学生白板权限');
  const studentList = students.value.filter(s => s.hasBoardPermission);
  
  for (const student of studentList) {
    await revokePermission(student.userId);
  }
};

// 刷新成员列表
const refreshMemberList = () => {
  fetchMemberList();
};

// 监听白板权限管理请求
const handleTaskUpdate = (event) => {
  if (event.detail.taskName === 'custom-question-tool' && event.detail.data.type === 'manage-board-permission') {
    console.log('收到白板权限管理请求');
    if (window.TCIC && TCIC.SDK.instance.isTeacher && TCIC.SDK.instance.isTeacher()) {
      console.log('准备显示白板权限管理弹窗');
      showModal.value = true;
      fetchMemberList();
    } else {
      console.log('当前用户不是老师，不显示白板权限管理弹窗');
    }
  }
};

// 组件挂载时初始化
onMounted(() => {
  console.log('boardPermissionModal组件挂载');
  document.addEventListener('tcic-task-update', handleTaskUpdate);
});

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('tcic-task-update', handleTaskUpdate);
});
</script>

<style lang="less" scoped>
/* 样式与boardPermissionModal.vue相同，这里省略以节省空间 */
/* 实际使用时需要复制完整的样式 */
.board-permission-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: modalSlideIn 0.3s ease-out;
}

/* 其他样式省略，与boardPermissionModal.vue相同 */
</style>
