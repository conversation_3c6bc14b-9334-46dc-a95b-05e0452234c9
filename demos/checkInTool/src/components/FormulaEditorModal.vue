<template>
  <div v-if="isFormulaEditorVisible" class="formula-modal-overlay" @click="closeModal">
    <div class="formula-modal" @click.stop>
      <div class="modal-header">
        <h3>学科公式编辑器</h3>
        <button class="close-btn" @click="closeModal">×</button>
      </div>
      
      <div class="modal-content">
        <div class="formula-editor">
          <!-- 公式类型选择 -->
          <div class="category-section">
            <h4>公式类型</h4>
            <div class="category-list">
              <button
                v-for="(category, index) in formulaCategories"
                :key="index"
                class="category-btn"
                :class="{ active: selectedCategory === index }"
                @click="selectCategory(index)"
              >
                {{ category.name }}
              </button>
            </div>
          </div>

          <!-- 公式模板 -->
          <div class="template-section">
            <h4>公式模板</h4>
            <div class="template-grid">
              <button
                v-for="(formula, index) in currentFormulas"
                :key="index"
                class="template-btn"
                @click="insertFormulaTemplate(formula)"
              >
                {{ formula.display }}
              </button>
            </div>
          </div>

          <!-- 编辑区域 -->
          <div class="editor-section">
            <h4>LaTeX 编辑</h4>
            <textarea
              v-model="formulaText"
              class="formula-input"
              placeholder="输入LaTeX公式代码..."
              rows="3"
            ></textarea>
            
            <div class="position-controls">
              <h5>插入位置</h5>
              <div class="position-inputs">
                <label>
                  水平位置 (%):
                  <input 
                    type="number" 
                    v-model.number="formulaPosition.left" 
                    min="0" 
                    max="100"
                    class="position-input"
                  >
                </label>
                <label>
                  垂直位置 (%):
                  <input 
                    type="number" 
                    v-model.number="formulaPosition.top" 
                    min="0" 
                    max="100"
                    class="position-input"
                  >
                </label>
              </div>
              
              <div class="quick-positions">
                <button @click="setPosition(10, 10)" class="pos-btn">左上</button>
                <button @click="setPosition(45, 10)" class="pos-btn">中上</button>
                <button @click="setPosition(80, 10)" class="pos-btn">右上</button>
                <button @click="setPosition(10, 45)" class="pos-btn">左中</button>
                <button @click="setPosition(45, 45)" class="pos-btn">居中</button>
                <button @click="setPosition(80, 45)" class="pos-btn">右中</button>
                <button @click="setPosition(10, 80)" class="pos-btn">左下</button>
                <button @click="setPosition(45, 80)" class="pos-btn">中下</button>
                <button @click="setPosition(80, 80)" class="pos-btn">右下</button>
              </div>
            </div>

            <div class="style-controls">
              <h5>样式设置</h5>
              <label>
                字体大小 (px):
                <input 
                  type="number" 
                  v-model.number="formulaStyle.fontSize" 
                  min="8" 
                  max="72"
                  class="style-input"
                >
              </label>
              <label>
                字体颜色:
                <input 
                  type="color" 
                  v-model="formulaStyle.fontColor"
                  class="color-input"
                >
              </label>
            </div>

            <div class="preview-section">
              <h5>预览</h5>
              <div class="formula-preview">
                {{ formulaText || '请输入公式...' }}
              </div>
            </div>

            <div class="action-buttons">
              <button class="insert-btn" @click="insertToBoard" :disabled="!formulaText.trim()">
                插入到白板
              </button>
              <button class="cancel-btn" @click="closeModal">
                取消
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue'
import { useFormulaEditor } from '../hooks/useFormulaEditor'

export default {
  name: 'FormulaEditorModal',
  setup() {
    const { isFormulaEditorVisible, formulaEditorData, hideFormulaEditor, insertFormula: insertFormulaToBoard } = useFormulaEditor();
    const selectedCategory = ref(0)
    const formulaText = ref('')
    
    const formulaPosition = reactive({
      left: 10,
      top: 10
    })
    
    const formulaStyle = reactive({
      fontSize: 16,
      fontColor: '#000000'
    })

    const formulaCategories = [
      {
        name: '基础数学',
        formulas: [
          { display: 'x²', latex: 'x^2' },
          { display: 'x₁', latex: 'x_1' },
          { display: '√x', latex: '\\sqrt{x}' },
          { display: '∛x', latex: '\\sqrt[3]{x}' },
          { display: 'x/y', latex: '\\frac{x}{y}' },
          { display: '∑', latex: '\\sum' },
          { display: '∫', latex: '\\int' },
          { display: '∞', latex: '\\infty' },
        ]
      },
      {
        name: '代数',
        formulas: [
          { display: 'ax²+bx+c=0', latex: 'ax^2+bx+c=0' },
          { display: '求根公式', latex: 'x=\\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}' },
          { display: '(a+b)²', latex: '(a+b)^2' },
          { display: '(a-b)²', latex: '(a-b)^2' },
          { display: 'a²-b²', latex: 'a^2-b^2' },
          { display: 'log_a(x)', latex: '\\log_a(x)' },
          { display: 'ln(x)', latex: '\\ln(x)' },
          { display: 'e^x', latex: 'e^x' },
        ]
      },
      {
        name: '几何',
        formulas: [
          { display: 'π', latex: '\\pi' },
          { display: '∠ABC', latex: '\\angle ABC' },
          { display: '△ABC', latex: '\\triangle ABC' },
          { display: '⊥', latex: '\\perp' },
          { display: '∥', latex: '\\parallel' },
          { display: '≅', latex: '\\cong' },
          { display: '∼', latex: '\\sim' },
          { display: '°', latex: '^\\circ' },
        ]
      },
      {
        name: '三角函数',
        formulas: [
          { display: 'sin(x)', latex: '\\sin(x)' },
          { display: 'cos(x)', latex: '\\cos(x)' },
          { display: 'tan(x)', latex: '\\tan(x)' },
          { display: '三角恒等式', latex: '\\sin^2(x)+\\cos^2(x)=1' },
          { display: 'sin(2x)', latex: '\\sin(2x)' },
          { display: 'cos(2x)', latex: '\\cos(2x)' },
          { display: 'arcsin(x)', latex: '\\arcsin(x)' },
          { display: 'arccos(x)', latex: '\\arccos(x)' },
        ]
      },
      {
        name: '微积分',
        formulas: [
          { display: 'lim', latex: '\\lim' },
          { display: 'dx/dy', latex: '\\frac{dx}{dy}' },
          { display: '∂f/∂x', latex: '\\frac{\\partial f}{\\partial x}' },
          { display: '∫f(x)dx', latex: '\\int f(x)dx' },
          { display: '∫ᵃᵇf(x)dx', latex: '\\int_a^b f(x)dx' },
          { display: '∬', latex: '\\iint' },
          { display: '∇', latex: '\\nabla' },
          { display: '∆', latex: '\\Delta' },
        ]
      },
      {
        name: '物理',
        formulas: [
          { display: 'F=ma', latex: 'F=ma' },
          { display: 'E=mc²', latex: 'E=mc^2' },
          { display: 'v=v₀+at', latex: 'v=v_0+at' },
          { display: '位移公式', latex: 's=v_0t+\\frac{1}{2}at^2' },
          { display: 'P=UI', latex: 'P=UI' },
          { display: 'F=qE', latex: 'F=qE' },
          { display: 'λ=c/f', latex: '\\lambda=\\frac{c}{f}' },
          { display: 'ω=2πf', latex: '\\omega=2\\pi f' },
        ]
      },
      {
        name: '化学',
        formulas: [
          { display: 'H₂O', latex: 'H_2O' },
          { display: 'CO₂', latex: 'CO_2' },
          { display: 'NaCl', latex: 'NaCl' },
          { display: 'H₂SO₄', latex: 'H_2SO_4' },
          { display: 'CaCO₃', latex: 'CaCO_3' },
          { display: '→', latex: '\\rightarrow' },
          { display: '⇌', latex: '\\rightleftharpoons' },
          { display: '↑', latex: '\\uparrow' },
        ]
      }
    ]

    const currentFormulas = computed(() => {
      return formulaCategories[selectedCategory.value]?.formulas || []
    })

    const selectCategory = (index) => {
      selectedCategory.value = index
    }

    const insertFormulaTemplate = (formula) => {
      formulaText.value = formula.latex
    }

    const setPosition = (left, top) => {
      formulaPosition.left = left
      formulaPosition.top = top
    }

    const insertToBoard = () => {
      if (formulaText.value.trim()) {
        const formulaData = {
          latex: formulaText.value,
          position: { ...formulaPosition },
          style: { ...formulaStyle }
        }
        insertFormulaToBoard(formulaData)
      }
    }

    const closeModal = () => {
      hideFormulaEditor()
    }

    return {
      isFormulaEditorVisible,
      selectedCategory,
      formulaText,
      formulaPosition,
      formulaStyle,
      formulaCategories,
      currentFormulas,
      selectCategory,
      insertFormulaTemplate,
      setPosition,
      insertToBoard,
      closeModal
    }
  }
}
</script>

<style scoped>
.formula-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
  box-sizing: border-box;
}

.formula-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s ease-out;
}

/* 弹窗出现动画 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-content {
  padding: 20px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.formula-editor {
  display: grid;
  grid-template-columns: 200px 300px 1fr;
  gap: 20px;
  min-height: 400px;
}

.category-section h4,
.template-section h4,
.editor-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
  text-align: left;
  transition: all 0.2s;
}

.category-btn:hover {
  background: #f0f0f0;
}

.category-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.template-btn {
  padding: 8px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.template-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.editor-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formula-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  resize: vertical;
}

.position-controls h5,
.style-controls h5 {
  margin: 0 0 8px 0;
  color: #555;
  font-size: 14px;
}

.position-inputs {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.position-inputs label {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.position-input,
.style-input {
  width: 80px;
  padding: 4px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.color-input {
  width: 50px;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.quick-positions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
}

.pos-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
  font-size: 12px;
}

.pos-btn:hover {
  background: #f0f0f0;
}

.formula-preview {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
  font-family: monospace;
  min-height: 40px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.insert-btn,
.cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.insert-btn {
  background: #007bff;
  color: white;
}

.insert-btn:hover:not(:disabled) {
  background: #0056b3;
}

.insert-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #545b62;
}
</style>
