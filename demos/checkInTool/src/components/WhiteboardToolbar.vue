<template>
  <div class="whiteboard-toolbar" v-show="!isCollapsed">
    <div class="toolbar-container">
      <!-- 1. 鼠标 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_MOUSE')"
          title="鼠标"
        >
          <i class="icon-cursor"></i>
        </button>
      </div>

      <!-- 2. 画笔 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_PEN')"
          title="画笔"
        >
          <i class="icon-pen"></i>
        </button>
      </div>

      <!-- 3. 形状 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_RECT')"
          title="形状"
        >
          <i class="icon-shape"></i>
        </button>
      </div>

      <!-- 4. 橡皮 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_ERASER')"
          title="橡皮"
        >
          <i class="icon-eraser"></i>
        </button>
      </div>

      <!-- 5. 框选 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_SELECT')"
          title="框选"
        >
          <i class="icon-select"></i>
        </button>
      </div>

      <!-- 6. 文本 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_TEXT')"
          title="文本"
        >
          <i class="icon-text"></i>
        </button>
      </div>

      <!-- 7. 激光笔 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_LASER')"
          title="激光笔"
        >
          <i class="icon-laser"></i>
        </button>
      </div>

      <!-- 8. 截图 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="captureScreen"
          title="截图"
        >
          <i class="icon-camera"></i>
        </button>
      </div>

      <!-- 9. 绘图工具 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_DRAW')"
          title="绘图工具"
        >
          <i class="icon-draw"></i>
        </button>
      </div>

      <!-- 10. 自定义图形 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="setToolType('TEDU_BOARD_TOOL_TYPE_CUSTOM')"
          title="自定义图形"
        >
          <i class="icon-custom"></i>
        </button>
      </div>

      <!-- 11. 调色盘 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="showColorPicker = !showColorPicker"
          title="调色盘"
        >
          <i class="icon-palette"></i>
        </button>
        <div v-if="showColorPicker" class="color-picker">
          <div 
            v-for="color in colors" 
            :key="color"
            class="color-item"
            :style="{ backgroundColor: color }"
            @click="setColor(color)"
          ></div>
        </div>
      </div>

      <!-- 12. 学科公式 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="showFormulaEditor"
          title="学科公式"
        >
          <i class="icon-formula"></i>
        </button>
      </div>

      <!-- 13. 撤销 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="undo"
          title="撤销"
        >
          <i class="icon-undo"></i>
        </button>
      </div>

      <!-- 14. 恢复 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="redo"
          title="恢复"
        >
          <i class="icon-redo"></i>
        </button>
      </div>

      <!-- 15. 收起 -->
      <div class="toolbar-item">
        <button 
          class="toolbar-btn" 
          @click="toggleToolbar"
          title="收起"
        >
          <i class="icon-collapse"></i>
        </button>
      </div>
    </div>


  </div>

  <!-- 收起状态的展开按钮 -->
  <div v-if="isCollapsed" class="toolbar-collapsed">
    <button 
      class="expand-btn" 
      @click="toggleToolbar"
      title="展开工具栏"
    >
      <i class="icon-expand"></i>
    </button>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'WhiteboardToolbar',
  setup() {
    const isCollapsed = ref(false)
    const showColorPicker = ref(false)
    
    const colors = [
      '#000000', '#FF0000', '#00FF00', '#0000FF',
      '#FFFF00', '#FF00FF', '#00FFFF', '#FFFFFF',
      '#808080', '#800000', '#008000', '#000080'
    ]

    const setToolType = (toolType) => {
      if (window.TEduBoard && window.TEduBoard.TEduBoardToolType) {
        window.teduBoard.setToolType(window.TEduBoard.TEduBoardToolType[toolType])
      }
    }

    const setColor = (color) => {
      if (window.teduBoard) {
        window.teduBoard.setBrushColor(color)
      }
      showColorPicker.value = false
    }

    const captureScreen = () => {
      if (window.teduBoard) {
        window.teduBoard.snapshot()
      }
    }

    const undo = () => {
      if (window.teduBoard) {
        window.teduBoard.undo()
      }
    }

    const redo = () => {
      if (window.teduBoard) {
        window.teduBoard.redo()
      }
    }

    const toggleToolbar = () => {
      isCollapsed.value = !isCollapsed.value
    }

    const showFormulaEditor = () => {
      // 触发全局公式编辑器显示
      const event = new CustomEvent('show-formula-editor', {
        bubbles: true,
        detail: {
          source: 'whiteboard-toolbar',
          callback: (formulaData) => {
            // 白板工具栏的公式插入回调
            if (window.teduBoard && window.TEduBoard) {
              try {
                window.teduBoard.addElement(
                  window.TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_FORMULA,
                  { expression: formulaData.latex },
                  {
                    erasable: true,
                    left: `${formulaData.position.left}%`,
                    top: `${formulaData.position.top}%`,
                    fontSize: `${formulaData.style.fontSize}px`,
                    fontColor: formulaData.style.fontColor + 'FF',
                  }
                )
                console.log('公式已从工具栏插入到白板:', formulaData);
              } catch (error) {
                console.error('插入公式失败:', error)
              }
            }
          }
        }
      });
      document.dispatchEvent(event);
    }

    return {
      isCollapsed,
      showColorPicker,
      colors,
      setToolType,
      setColor,
      captureScreen,
      undo,
      redo,
      toggleToolbar,
      showFormulaEditor
    }
  }
}
</script>

<style scoped>
.whiteboard-toolbar {
  position: fixed;
  right: 10px;
  top: 20%;
  transform: translateY(-20%);
  z-index: 1000;
  max-height: 60vh;
  overflow-y: auto;
}

.toolbar-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 6px 3px;
  width: 45px;
}

.toolbar-item {
  position: relative;
  margin-bottom: 4px;
}

.toolbar-btn {
  width: 38px;
  height: 38px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.toolbar-btn:hover {
  background-color: #f0f0f0;
}

.toolbar-btn:active {
  background-color: #e0e0e0;
}

.color-picker {
  position: absolute;
  right: 50px;
  top: 0;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 8px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
  width: 120px;
  z-index: 1001;
}

.color-item {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  cursor: pointer;
  border: 1px solid #ddd;
}

.toolbar-collapsed {
  position: fixed;
  right: 10px;
  top: 20%;
  transform: translateY(-20%);
  z-index: 1000;
}

.expand-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图标样式 - 使用CSS实现简单图标 */
.icon-cursor::before { content: "🖱️"; }
.icon-pen::before { content: "✏️"; }
.icon-shape::before { content: "⬜"; }
.icon-eraser::before { content: "🧽"; }
.icon-select::before { content: "⬚"; }
.icon-text::before { content: "T"; font-weight: bold; }
.icon-laser::before { content: "🔴"; }
.icon-camera::before { content: "📷"; }
.icon-draw::before { content: "🎨"; }
.icon-custom::before { content: "⭐"; }
.icon-palette::before { content: "🎨"; }
.icon-formula::before { content: "∑"; font-weight: bold; }
.icon-undo::before { content: "↶"; }
.icon-redo::before { content: "↷"; }
.icon-collapse::before { content: "◀"; }
.icon-expand::before { content: "▶"; }
</style>
