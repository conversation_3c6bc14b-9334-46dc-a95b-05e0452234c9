<template>
  <div class="custom-tools-container">
    <div
      class="custom-question-btn"
      @click="showQuestions"
    >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z" fill="#a3aec7"/>
      </svg>
      <span class="header__btn-text">提问</span>
    </div>
  </div>
</template>

<script setup>
import useTask from '../hooks/useTask';

// 问题工具的task管理
const { updateTask: updateQuestionTask } = useTask('custom-question-tool');

const showQuestions = () => {
  console.log('老师点击了提问按钮');
  console.log('当前用户角色:', TCIC.SDK.instance.isTeacher() ? '老师' : '学生');
  updateQuestionTask({
    type: 'show-questions',
  });
  console.log('已发送show-questions任务');
};


</script>

<style lang="less">
.custom-tools-container {
  display: flex;
  height: 100%;

  .custom-question-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    width: 60px;
    cursor: pointer;

    span {
      font-size: 12px;
      color: #a3aec7;
      line-height: 19px;
    }

    img,
    svg {
      width: 24px;
      height: 24px;
    }

    &:hover {
      background-color: rgba(163, 174, 199, 0.1);

      span {
        color: #7c8db5;
      }

      svg path {
        fill: #7c8db5;
      }
    }
  }
}
</style>