<template>
  <div
    v-if="showModal"
    class="checkin-modal"
  >
    <div class="checkin-modal__content">
      <div class="checkin-modal__header">
        <h2>签到</h2>
        <button @click="showModal = false">
          关闭
        </button>
      </div>
      <div class="checkin-modal__body">
        <div class="checkin-content">
          <div class="checkin-icon">✅</div>
          <p class="checkin-title">课堂签到</p>
          <p class="checkin-desc">请确认您已准备好参与课堂学习</p>
          <div v-if="loading" class="loading-indicator">
            <span class="spinner"></span>
            <span>正在签到...</span>
          </div>
        </div>
      </div>
      <div class="checkin-modal__footer">
        <button
          @click="handleCancel"
          :disabled="loading"
          class="btn-cancel"
        >
          取消
        </button>
        <button
          @click="handleCheckIn"
          :disabled="loading"
          class="btn-confirm"
        >
          <span v-if="!loading">确认签到</span>
          <span v-else>签到中...</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import useTask from '../hooks/useTask';

const showModal = ref(false);
const isCheckIn = ref(false);
const checkInTime = ref(null);
const loading = ref(false);

// 从localStorage恢复签到状态
onMounted(() => {
  const savedCheckInStatus = localStorage.getItem('student-checkin-status');
  if (savedCheckInStatus) {
    try {
      const status = JSON.parse(savedCheckInStatus);
      isCheckIn.value = status.isCheckIn;
      checkInTime.value = status.checkInTime;
    } catch (error) {
      console.warn('恢复签到状态失败:', error);
    }
  }
});

// 监听签到请求
useTask('custom-check-in-tool', (data) => {
  // 任务更新回调
  if (data.type === 'ask-check-in') {
    console.log('收到签到请求:', data);

    // 检查是否已签到
    if (!isCheckIn.value) {
      // 等待设备检测完成后展示弹窗
      TCIC.SDK.instance.promiseState('TStateDeviceDetect', false).then(() => {
        // 确保课堂已经开始
        return TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start);
      }).then(() => {
        showModal.value = true;
      }).catch((error) => {
        console.error('等待课堂状态失败:', error);
        // 即使状态检查失败，也显示签到弹窗
        showModal.value = true;
      });
    } else {
      // 已经签到，使用TCIC的消息提示
      if (typeof TCIC.SDK.instance.showMessageBox === 'function') {
        TCIC.SDK.instance.showMessageBox(
          '签到提示',
          `您已于 ${checkInTime.value} 完成签到`,
          ['确定']
        );
      } else {
        console.log('已经签到，时间:', checkInTime.value);
      }
    }
  }
});

const { updateTask } = useTask('custom-check-in-tool-result');

const handleCheckIn = async () => {
  if (loading.value) return;

  loading.value = true;

  try {
    const currentTime = new Date().toLocaleString();
    const userId = TCIC.SDK.instance.getUserId();
    const classInfo = TCIC.SDK.instance.getClassInfo();

    // 更新本地状态
    isCheckIn.value = true;
    checkInTime.value = currentTime;
    showModal.value = false;

    // 保存到localStorage
    const checkInStatus = {
      isCheckIn: true,
      checkInTime: currentTime,
      userId: userId,
      classId: classInfo?.classId
    };
    localStorage.setItem('student-checkin-status', JSON.stringify(checkInStatus));

    // 这里可以添加向服务器发送签到请求的逻辑
    // await sendCheckInToServer(checkInStatus);

    // 告知老师签到完成
    updateTask({
      type: 'student-check-in',
      payload: {
        checkIn: true,
        userId: userId,
        checkInTime: currentTime,
        timestamp: Date.now()
      },
    });

    // 显示成功提示
    if (typeof TCIC.SDK.instance.showMessageBox === 'function') {
      TCIC.SDK.instance.showMessageBox(
        '签到成功',
        '您已成功完成签到！',
        ['确定']
      );
    }

  } catch (error) {
    console.error('签到失败:', error);
    // 恢复状态
    isCheckIn.value = false;
    checkInTime.value = null;

    // 显示错误提示
    if (typeof TCIC.SDK.instance.showMessageBox === 'function') {
      TCIC.SDK.instance.showMessageBox(
        '签到失败',
        '签到过程中出现错误，请重试',
        ['确定']
      );
    }
  } finally {
    loading.value = false;
  }
};

// 取消签到
const handleCancel = () => {
  showModal.value = false;
};

// 组件卸载时清理
onUnmounted(() => {
  // 可以在这里添加清理逻辑
});
</script>

<style lang="less">
.checkin-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(4px);

  .checkin-modal__content {
    background-color: #fff;
    border-radius: 12px;
    width: 380px;
    max-width: 90vw;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;

    .checkin-modal__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }

      button {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 6px;
        color: white;
        cursor: pointer;
        font-size: 14px;
        padding: 6px 12px;
        transition: background-color 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }

    .checkin-modal__body {
      padding: 30px 24px;

      .checkin-content {
        text-align: center;

        .checkin-icon {
          font-size: 48px;
          margin-bottom: 16px;
          animation: pulse 2s infinite;
        }

        .checkin-title {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin: 0 0 8px 0;
        }

        .checkin-desc {
          font-size: 14px;
          color: #666;
          margin: 0 0 20px 0;
          line-height: 1.5;
        }

        .loading-indicator {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          color: #667eea;
          font-size: 14px;

          .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }
      }
    }

    .checkin-modal__footer {
      display: flex;
      gap: 12px;
      padding: 20px 24px;
      background: #f8f9fa;

      button {
        flex: 1;
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.btn-cancel {
          background: #f5f5f5;
          color: #666;

          &:hover:not(:disabled) {
            background: #e9e9e9;
          }
        }

        &.btn-confirm {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 480px) {
  .checkin-modal {
    .checkin-modal__content {
      width: 95vw;
      margin: 20px;

      .checkin-modal__header,
      .checkin-modal__body,
      .checkin-modal__footer {
        padding-left: 20px;
        padding-right: 20px;
      }

      .checkin-modal__body {
        padding-top: 24px;
        padding-bottom: 24px;

        .checkin-content {
          .checkin-icon {
            font-size: 40px;
          }

          .checkin-title {
            font-size: 18px;
          }
        }
      }
    }
  }
}
</style>