<template>
  <div
    v-if="showModal"
    class="question-modal"
  >
    <div class="question-modal__content">
      <div class="question-modal__header">
        <h2>课前提问</h2>
        <div class="question-modal__controls">
          <span v-if="hasMultipleQuestions" class="question-counter">
            {{ currentQuestionIndex + 1 }} / {{ questions.length }}
          </span>
          <button @click="refreshQuestions" class="refresh-btn">刷新</button>
          <button @click="showModal = false" class="close-btn">关闭</button>
        </div>
      </div>
      <div class="question-modal__body">
        <div v-if="loading" class="loading">
          <div class="loading-spinner"></div>
          <p>加载中...</p>
        </div>
        <div v-else-if="questions.length === 0" class="no-questions">
          <div class="empty-icon">📝</div>
          <p>暂无课前提问</p>
        </div>
        <div v-else class="questions-container">
          <!-- 导航按钮 -->
          <button
            v-if="hasMultipleQuestions && canGoPrev"
            @click="prevQuestion"
            class="nav-btn nav-btn-prev"
            aria-label="上一个问题"
          >
            ‹
          </button>

          <!-- 问题内容区域 -->
          <div
            class="question-content"
            @touchstart="handleTouchStart"
            @touchend="handleTouchEnd"
          >
            <div class="current-question">
              <div
                v-for="(message, msgIndex) in currentQuestion"
                :key="msgIndex"
                class="message-item"
                :class="message.message_type"
              >
                <div v-if="message.message_type === 'text'" class="text-message">
                  <div v-html="renderTextWithFormulas(message.content)"></div>
                </div>
                <div v-else-if="message.message_type === 'image'" class="image-message">
                  <img :src="message.content" alt="学生提问图片" @error="handleImageError" />
                </div>
              </div>
            </div>
          </div>

          <!-- 导航按钮 -->
          <button
            v-if="hasMultipleQuestions && canGoNext"
            @click="nextQuestion"
            class="nav-btn nav-btn-next"
            aria-label="下一个问题"
          >
            ›
          </button>
        </div>

        <!-- 问题指示器 -->
        <div v-if="hasMultipleQuestions" class="question-indicators">
          <button
            v-for="(_, index) in questions"
            :key="index"
            @click="goToQuestion(index)"
            class="indicator-dot"
            :class="{ active: index === currentQuestionIndex }"
            :aria-label="`跳转到第 ${index + 1} 个问题`"
          ></button>
        </div>
      </div>
      <div class="question-modal__footer">
        <div class="footer-info">
          <span class="question-count">共 {{ questions.length }} 个提问</span>
          <span v-if="hasMultipleQuestions" class="navigation-hint">
            使用 ← → 键或滑动切换问题
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import useTask from '../hooks/useTask';

const showModal = ref(false);
const questions = ref([]);
const loading = ref(false);
const currentQuestionIndex = ref(0);
const touchStartX = ref(0);
const touchEndX = ref(0);

// 计算属性
const currentQuestion = computed(() => {
  const question = questions.value[currentQuestionIndex.value] || [];

  // 将文字消息排在前面，图片消息排在后面
  const textMessages = question.filter(msg => msg.message_type === 'text');
  const imageMessages = question.filter(msg => msg.message_type === 'image');

  return [...textMessages, ...imageMessages];
});

const hasMultipleQuestions = computed(() => {
  return questions.value.length > 1;
});

const canGoPrev = computed(() => {
  return currentQuestionIndex.value > 0;
});

const canGoNext = computed(() => {
  return currentQuestionIndex.value < questions.value.length - 1;
});

// 功能配置状态（参考白板权限配置模式）
const featureConfig = ref({
  allowQuestionView: true,      // 是否允许查看问题
  allowQuestionRefresh: true,   // 是否允许刷新问题
  allowQuestionNavigation: true, // 是否允许切换问题
  allowKeyboardControl: true,   // 是否允许键盘控制
  allowTouchControl: true       // 是否允许触摸控制
});

// 设置功能可用性（参考 TCIC.SDK.instance.setFeatureAvailable）
const setQuestionFeatureAvailable = (feature, available) => {
  if (featureConfig.value.hasOwnProperty(feature)) {
    featureConfig.value[feature] = available;
    console.log(`问题查看功能 ${feature} 设置为:`, available);
  } else {
    console.warn(`未知的功能配置项: ${feature}`);
  }
};

// 检查功能是否可用
const isFeatureAvailable = (feature) => {
  return featureConfig.value[feature] !== false;
};

// 渲染包含公式的文本
const renderTextWithFormulas = (text) => {
  if (!text) return '';

  // 检测LaTeX公式模式：$...$ 或 $$...$$ 或 \(...\) 或 \[...\]
  const formulaPatterns = [
    /\$\$([^$]+)\$\$/g,  // $$...$$
    /\$([^$]+)\$/g,      // $...$
    /\\\(([^)]+)\\\)/g,  // \(...\)
    /\\\[([^\]]+)\\\]/g  // \[...\]
  ];

  let processedText = text;
  let formulaIndex = 0;

  // 替换所有公式为占位符，并收集公式
  const formulas = [];
  formulaPatterns.forEach(pattern => {
    processedText = processedText.replace(pattern, (match, formula) => {
      const placeholder = `__FORMULA_${formulaIndex}__`;
      formulas.push({
        placeholder,
        formula: formula.trim(),
        isBlock: match.startsWith('$$') || match.startsWith('\\[')
      });
      formulaIndex++;
      return placeholder;
    });
  });

  // 转义HTML特殊字符
  processedText = processedText
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');

  // 替换占位符为渲染后的公式
  formulas.forEach(({ placeholder, formula, isBlock }) => {
    const formulaHtml = renderFormula(formula, isBlock);
    processedText = processedText.replace(placeholder, formulaHtml);
  });

  return processedText;
};

// 渲染单个公式
const renderFormula = (formula, isBlock = false) => {
  try {
    // 如果有KaTeX可用，使用KaTeX渲染
    if (window.katex) {
      const html = window.katex.renderToString(formula, {
        throwOnError: false,
        displayMode: isBlock
      });
      return `<span class="formula-container ${isBlock ? 'block' : 'inline'}">${html}</span>`;
    }

    // 如果有MathJax可用，使用MathJax渲染
    if (window.MathJax) {
      const wrapper = isBlock ? '\\[' + formula + '\\]' : '\\(' + formula + '\\)';
      return `<span class="formula-container ${isBlock ? 'block' : 'inline'}">${wrapper}</span>`;
    }

    // 如果没有数学渲染库，显示原始LaTeX
    const wrapper = isBlock ? '$$' + formula + '$$' : '$' + formula + '$';
    return `<code class="formula-raw">${wrapper}</code>`;
  } catch (error) {
    console.warn('公式渲染失败:', formula, error);
    return `<code class="formula-error">公式渲染错误: ${formula}</code>`;
  }
};

// 获取classid的函数
const getClassId = () => {
  // 从URL参数中获取classid，或者从TCIC SDK中获取
  const urlParams = new URLSearchParams(window.location.search);
  const classIdFromUrl = urlParams.get('classid');
  
  if (classIdFromUrl) {
    return classIdFromUrl;
  }
  
  // 如果URL中没有，尝试从TCIC SDK获取
  try {
    const classInfo = TCIC.SDK.instance.getClassInfo();
    return classInfo?.classId || '245783322'; // 默认值
  } catch (error) {
    console.warn('无法获取classId，使用默认值', error);
    return '245783322';
  }
};

// 获取课前提问数据
const fetchQuestions = async () => {
  console.log('开始获取课前提问数据');
  loading.value = true;
  try {
    const classId = getClassId();
    console.log('使用的classId:', classId);
    const apiUrl = `https://api.kaohaolaai.com/api/o/resource/pre/question?classid=${classId}`;
    console.log('请求URL:', apiUrl);

    const response = await fetch(apiUrl);
    console.log('API响应状态:', response.status);
    const result = await response.json();
    console.log('API响应数据:', result);

    if (result.code === 100000 && result.data) {
      questions.value = result.data;
      console.log('成功获取问题数据，问题数量:', result.data.length);
      currentQuestionIndex.value = 0; // 重置到第一个问题
    } else {
      console.error('获取问题失败:', result.msg);
      questions.value = [];
    }
  } catch (error) {
    console.error('请求失败:', error);
    questions.value = [];
  } finally {
    loading.value = false;
    console.log('问题获取完成，loading状态:', loading.value);
  }
};

// 刷新问题（检查权限）
const refreshQuestions = () => {
  if (!isFeatureAvailable('allowQuestionRefresh')) {
    console.log('刷新问题功能已禁用');
    return;
  }
  fetchQuestions();
};

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none';
  console.error('图片加载失败:', event.target.src);
};

// 切换到上一个问题（检查权限）
const prevQuestion = () => {
  if (!isFeatureAvailable('allowQuestionNavigation')) {
    console.log('问题导航功能已禁用');
    return;
  }
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--;
  }
};

// 切换到下一个问题（检查权限）
const nextQuestion = () => {
  if (!isFeatureAvailable('allowQuestionNavigation')) {
    console.log('问题导航功能已禁用');
    return;
  }
  if (currentQuestionIndex.value < questions.value.length - 1) {
    currentQuestionIndex.value++;
  }
};

// 跳转到指定问题（检查权限）
const goToQuestion = (index) => {
  if (!isFeatureAvailable('allowQuestionNavigation')) {
    console.log('问题导航功能已禁用');
    return;
  }
  if (index >= 0 && index < questions.value.length) {
    currentQuestionIndex.value = index;
  }
};

// 触摸开始
const handleTouchStart = (event) => {
  touchStartX.value = event.touches[0].clientX;
};

// 触摸结束
const handleTouchEnd = (event) => {
  touchEndX.value = event.changedTouches[0].clientX;
  handleSwipe();
};

// 处理滑动（检查权限）
const handleSwipe = () => {
  // 检查触摸控制权限
  if (!isFeatureAvailable('allowTouchControl')) {
    return;
  }

  const swipeThreshold = 50; // 滑动阈值
  const diff = touchStartX.value - touchEndX.value;

  if (Math.abs(diff) > swipeThreshold) {
    if (diff > 0) {
      // 向左滑动，显示下一个问题
      nextQuestion();
    } else {
      // 向右滑动，显示上一个问题
      prevQuestion();
    }
  }
};

// 键盘事件处理（检查权限）
const handleKeydown = (event) => {
  if (!showModal.value) return;

  // 检查键盘控制权限
  if (!isFeatureAvailable('allowKeyboardControl')) {
    return;
  }

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault();
      prevQuestion();
      break;
    case 'ArrowRight':
      event.preventDefault();
      nextQuestion();
      break;
    case 'Escape':
      event.preventDefault();
      showModal.value = false;
      break;
  }
};

// 监听老师发起的查看问题请求
useTask('custom-question-tool', (data) => {
  console.log('questionModal收到任务更新:', data);
  if (data.type === 'show-questions') {
    console.log('收到查看问题请求');
    console.log('当前用户是否为老师:', TCIC.SDK.instance.isTeacher());
    // 只有老师才能看到课前提问
    if (TCIC.SDK.instance.isTeacher()) {
      console.log('准备显示问题弹窗');
      showModal.value = true;
      fetchQuestions();
    } else {
      console.log('当前用户不是老师，不显示问题弹窗');
    }
  }
});

// 监听课堂权限变化（参考白板权限配置文档）
TCIC.SDK.instance.subscribeState && TCIC.SDK.instance.subscribeState(TCIC.TMainState.Class_Status, (status) => {
  console.log('课堂状态变化:', status);
  // 当课堂状态变化时，重新检查权限
  if (status === TCIC.TClassStatus.Already_Start) {
    console.log('课堂已开始，重新检查用户权限');
  }
});

// 监听成员权限变化（如果有相关事件）
if (TCIC.SDK.instance.subscribeState && TCIC.TMainState.Board_Permission) {
  TCIC.SDK.instance.subscribeState(TCIC.TMainState.Board_Permission, (value) => {
    console.log('权限状态变化:', value);
    // 可以根据权限变化调整UI显示
  });
}

// 加载数学公式渲染库
const loadMathLibrary = () => {
  // 检查是否已经加载了KaTeX
  if (window.katex) {
    console.log('KaTeX已加载');
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    // 加载KaTeX CSS
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css';
    cssLink.onload = () => {
      // 加载KaTeX JS
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js';
      script.onload = () => {
        console.log('KaTeX加载成功');
        resolve();
      };
      script.onerror = () => {
        console.warn('KaTeX加载失败，将使用原始LaTeX显示');
        resolve(); // 不阻塞，继续执行
      };
      document.head.appendChild(script);
    };
    cssLink.onerror = () => {
      console.warn('KaTeX CSS加载失败');
      resolve(); // 不阻塞，继续执行
    };
    document.head.appendChild(cssLink);
  });
};

// 组件挂载时初始化（参考白板权限配置最佳实践）
onMounted(() => {
  console.log('questionModal组件挂载');

  // 加载数学公式渲染库
  loadMathLibrary();

  // 等待课堂状态就绪后再初始化（参考白板权限文档）
  TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
    console.log('课堂已开始，开始初始化问题查看功能');

    // 只有老师才初始化问题查看功能
    if (!TCIC.SDK.instance.isTeacher()) {
      console.log('当前用户不是老师，跳过问题查看功能初始化');
      return;
    }

    console.log('老师用户，初始化问题查看功能');

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeydown);

    // 可以在这里添加更多老师专用的功能初始化
    console.log('问题查看功能初始化完成');
  }).catch(error => {
    console.error('等待课堂状态时出错:', error);
  });
});

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style lang="less">
.question-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  
  .question-modal__content {
    background-color: #fff;
    border-radius: 8px;
    width: 600px;
    max-width: 90vw;
    max-height: 80vh;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    
    .question-modal__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #eee;
      
      h2 {
        margin: 0;
        color: #333;
        font-size: 18px;
      }
      
      .question-modal__controls {
        display: flex;
        align-items: center;
        gap: 10px;

        .question-counter {
          background: rgba(255, 255, 255, 0.2);
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        button {
          background: none;
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 4px;
          padding: 6px 12px;
          cursor: pointer;
          font-size: 14px;
          color: black;
          transition: all 0.2s ease;

          &.refresh-btn {
            background-color: rgba(255, 255, 255, 0.1);

            &:hover {
              background-color: rgba(255, 255, 255, 0.2);
            }
          }

          &.close-btn {
            &:hover {
              background-color: rgba(255, 255, 255, 0.2);
            }
          }
        }
      }
    }
    
    .question-modal__body {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      
      .loading {
        text-align: center;
        padding: 60px 20px;
        color: #666;

        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }
      }

      .no-questions {
        text-align: center;
        padding: 60px 20px;
        color: #666;

        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
        }
      }

      .questions-container {
        position: relative;
        display: flex;
        align-items: center;
        min-height: 300px;

        .nav-btn {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 40px;
          height: 40px;
          border: none;
          border-radius: 50%;
          background: rgba(102, 126, 234, 0.1);
          color: #667eea;
          font-size: 24px;
          font-weight: bold;
          cursor: pointer;
          z-index: 10;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-50%) scale(1.1);
          }

          &.nav-btn-prev {
            left: -20px;
          }

          &.nav-btn-next {
            right: -20px;
          }
        }

        .question-content {
          flex: 1;
          padding: 0 30px;
          touch-action: pan-y;

          .current-question {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            background-color: #f9fafb;
            min-height: 200px;

            .message-item {
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }

              .text-message {
                padding: 16px;
                background-color: white;
                border-radius: 8px;
                border-left: 4px solid #3b82f6;
                font-size: 15px;
                line-height: 1.6;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

                // 公式样式
                .formula-container {
                  &.inline {
                    display: inline;
                    margin: 0 2px;
                  }

                  &.block {
                    display: block;
                    text-align: center;
                    margin: 10px 0;
                    padding: 8px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                  }
                }

                .formula-raw {
                  background-color: #f1f3f4;
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-family: 'Courier New', monospace;
                  font-size: 0.9em;
                  color: #5f6368;
                }

                .formula-error {
                  background-color: #fef2f2;
                  color: #dc2626;
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-family: 'Courier New', monospace;
                  font-size: 0.9em;
                  border: 1px solid #fecaca;
                }
              }

              .image-message {
                text-align: center;

                img {
                  max-width: 100%;
                  max-height: 400px;
                  border-radius: 8px;
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                  cursor: pointer;
                  transition: transform 0.2s ease;

                  &:hover {
                    transform: scale(1.02);
                  }
                }
              }
            }
          }
        }
      }

      .question-indicators {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin-top: 20px;

        .indicator-dot {
          width: 10px;
          height: 10px;
          border: none;
          border-radius: 50%;
          background: #d1d5db;
          cursor: pointer;
          transition: all 0.2s ease;

          &.active {
            background: #667eea;
            transform: scale(1.2);
          }

          &:hover:not(.active) {
            background: #9ca3af;
            transform: scale(1.1);
          }
        }
      }
      
      .questions-list {
        .question-group {
          margin-bottom: 20px;
          padding: 15px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          background-color: #f9fafb;
          
          .message-item {
            margin-bottom: 10px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .text-message {
              padding: 10px;
              background-color: white;
              border-radius: 6px;
              border-left: 3px solid #3b82f6;
              font-size: 14px;
              line-height: 1.5;
            }
            
            .image-message {
              img {
                max-width: 100%;
                max-height: 300px;
                border-radius: 6px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                cursor: pointer;
                
                &:hover {
                  transform: scale(1.02);
                  transition: transform 0.2s ease;
                }
              }
            }
          }
        }
      }
    }
    
    .question-modal__footer {
      padding: 16px 24px;
      border-top: 1px solid #eee;
      background-color: #f8f9fa;

      .footer-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;

        .question-count {
          margin: 0;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .navigation-hint {
          font-size: 12px;
          color: #999;
          font-style: italic;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .question-modal {
    .question-modal__content {
      width: 95vw;
      max-height: 90vh;

      .question-modal__header {
        padding: 16px;

        h2 {
          font-size: 16px;
        }

        .question-modal__controls {
          gap: 8px;

          .question-counter {
            font-size: 11px;
            padding: 3px 6px;
          }

          button {
            padding: 4px 8px;
            font-size: 12px;
          }
        }
      }

      .question-modal__body {
        padding: 16px;

        .questions-container {
          min-height: 250px;

          .nav-btn {
            width: 36px;
            height: 36px;
            font-size: 20px;

            &.nav-btn-prev {
              left: -18px;
            }

            &.nav-btn-next {
              right: -18px;
            }
          }

          .question-content {
            padding: 0 20px;

            .current-question {
              padding: 16px;
              min-height: 180px;

              .message-item {
                .text-message {
                  padding: 12px;
                  font-size: 14px;
                }

                .image-message img {
                  max-height: 300px;
                }
              }
            }
          }
        }

        .question-indicators {
          margin-top: 16px;
          gap: 6px;

          .indicator-dot {
            width: 8px;
            height: 8px;
          }
        }
      }

      .question-modal__footer {
        padding: 12px 16px;

        .footer-info {
          flex-direction: column;
          align-items: center;
          gap: 4px;

          .question-count {
            font-size: 13px;
          }

          .navigation-hint {
            font-size: 11px;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
