<template>
  <div v-if="showModal" class="board-permission-modal">
    <div class="board-permission-modal__content">
      <div class="board-permission-modal__header">
        <h2>🎨 白板权限管理</h2>
        <div class="board-permission-modal__controls">
          <button @click="refreshMemberList" class="refresh-btn">刷新</button>
          <button @click="showModal = false" class="close-btn">关闭</button>
        </div>
      </div>

      <div class="board-permission-modal__body">
        <div v-if="loading" class="loading">
          <div class="loading-spinner"></div>
          <p>加载成员列表中...</p>
        </div>
        
        <div v-else-if="members.length === 0" class="no-members">
          <div class="empty-icon">👥</div>
          <p>暂无在线成员</p>
        </div>
        
        <div v-else class="members-list">
          <div class="batch-controls">
            <button @click="grantAllStudents" class="batch-btn grant-all">
              ✅ 全部授权
            </button>
            <button @click="revokeAllStudents" class="batch-btn revoke-all">
              ❌ 全部收回
            </button>
          </div>
          
          <div class="member-item" v-for="member in students" :key="member.userId">
            <div class="member-info">
              <div class="member-avatar">
                <img v-if="member.avatar" :src="member.avatar" :alt="member.name" />
                <div v-else class="default-avatar">{{ member.name?.charAt(0) || '?' }}</div>
              </div>
              <div class="member-details">
                <div class="member-name">{{ member.name || member.userId }}</div>
                <div class="member-status">
                  <span class="role-tag">学生</span>
                  <span :class="['permission-status', member.hasBoardPermission ? 'granted' : 'denied']">
                    {{ member.hasBoardPermission ? '已授权' : '未授权' }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="member-actions">
              <button 
                v-if="!member.hasBoardPermission"
                @click="grantPermission(member.userId)"
                class="action-btn grant-btn"
              >
                授权
              </button>
              <button 
                v-else
                @click="revokePermission(member.userId)"
                class="action-btn revoke-btn"
              >
                收回
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="board-permission-modal__footer">
        <div class="footer-info">
          <span class="member-count">在线学生: {{ students.length }} 人</span>
          <span class="permission-count">已授权: {{ authorizedCount }} 人</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import useTask from '../hooks/useTask';

// 响应式数据
const showModal = ref(false);
const loading = ref(false);
const members = ref([]);

// 计算属性
const students = computed(() => {
  return members.value.filter(member => member.role === 'student');
});

const authorizedCount = computed(() => {
  return students.value.filter(member => member.hasBoardPermission).length;
});

// 获取成员列表
const fetchMemberList = async () => {
  loading.value = true;
  try {
    // 获取TCIC成员列表
    const memberList = TCIC.SDK.instance.getMemberList ? TCIC.SDK.instance.getMemberList() : [];
    
    // 检查每个成员的白板权限状态
    const membersWithPermission = await Promise.all(
      memberList.map(async (member) => {
        let hasBoardPermission = false;
        
        try {
          // 检查白板权限状态
          hasBoardPermission = await checkBoardPermission(member.userId);
        } catch (error) {
          console.warn(`检查用户 ${member.userId} 白板权限失败:`, error);
        }
        
        return {
          ...member,
          hasBoardPermission
        };
      })
    );
    
    members.value = membersWithPermission;
    console.log('成员列表获取成功:', members.value);
  } catch (error) {
    console.error('获取成员列表失败:', error);
    members.value = [];
  } finally {
    loading.value = false;
  }
};

// 检查用户白板权限
const checkBoardPermission = async (userId) => {
  try {
    // 这里需要根据实际的TCIC API来检查权限
    // 示例实现
    const teduBoard = TCIC.SDK.instance.getBoard && TCIC.SDK.instance.getBoard();
    if (teduBoard && teduBoard.getUserPermission) {
      const permission = teduBoard.getUserPermission(userId);
      return permission && permission.canDraw;
    }
    return false;
  } catch (error) {
    console.error('检查白板权限失败:', error);
    return false;
  }
};

// 授权白板权限
const grantPermission = async (userId) => {
  try {
    console.log(`授权用户 ${userId} 白板权限`);
    
    // 方法1：使用TCIC SDK设置权限
    if (TCIC.SDK.instance.setBoardPermission) {
      await TCIC.SDK.instance.setBoardPermission(userId, true);
    }
    
    // 方法2：使用白板实例设置权限
    const teduBoard = TCIC.SDK.instance.getBoard && TCIC.SDK.instance.getBoard();
    if (teduBoard && teduBoard.setPermission) {
      teduBoard.setPermission(userId, {
        canDraw: true,
        canOperate: true,
        canAddPage: false,
        canDeletePage: false
      });
    }
    
    // 更新本地状态
    const member = members.value.find(m => m.userId === userId);
    if (member) {
      member.hasBoardPermission = true;
    }
    
    console.log(`用户 ${userId} 白板权限授权成功`);
  } catch (error) {
    console.error(`授权用户 ${userId} 白板权限失败:`, error);
  }
};

// 收回白板权限
const revokePermission = async (userId) => {
  try {
    console.log(`收回用户 ${userId} 白板权限`);
    
    // 方法1：使用TCIC SDK收回权限
    if (TCIC.SDK.instance.setBoardPermission) {
      await TCIC.SDK.instance.setBoardPermission(userId, false);
    }
    
    // 方法2：使用白板实例收回权限
    const teduBoard = TCIC.SDK.instance.getBoard && TCIC.SDK.instance.getBoard();
    if (teduBoard && teduBoard.setPermission) {
      teduBoard.setPermission(userId, {
        canDraw: false,
        canOperate: false,
        canAddPage: false,
        canDeletePage: false
      });
    }
    
    // 更新本地状态
    const member = members.value.find(m => m.userId === userId);
    if (member) {
      member.hasBoardPermission = false;
    }
    
    console.log(`用户 ${userId} 白板权限收回成功`);
  } catch (error) {
    console.error(`收回用户 ${userId} 白板权限失败:`, error);
  }
};

// 批量授权所有学生
const grantAllStudents = async () => {
  console.log('批量授权所有学生白板权限');
  const studentList = students.value.filter(s => !s.hasBoardPermission);
  
  for (const student of studentList) {
    await grantPermission(student.userId);
  }
};

// 批量收回所有学生权限
const revokeAllStudents = async () => {
  console.log('批量收回所有学生白板权限');
  const studentList = students.value.filter(s => s.hasBoardPermission);
  
  for (const student of studentList) {
    await revokePermission(student.userId);
  }
};

// 刷新成员列表
const refreshMemberList = () => {
  fetchMemberList();
};

// 监听老师发起的白板权限管理请求
useTask('custom-question-tool', (data) => {
  console.log('boardPermissionModal收到任务更新:', data);
  if (data.type === 'manage-board-permission') {
    console.log('收到白板权限管理请求');
    // 只有老师才能管理白板权限
    if (TCIC.SDK.instance.isTeacher()) {
      console.log('准备显示白板权限管理弹窗');
      showModal.value = true;
      fetchMemberList();
    } else {
      console.log('当前用户不是老师，不显示白板权限管理弹窗');
    }
  }
});

// 监听成员变化
const handleMemberChange = () => {
  console.log('成员列表发生变化，刷新数据');
  if (showModal.value) {
    fetchMemberList();
  }
};

// 组件挂载时初始化
onMounted(() => {
  console.log('boardPermissionModal组件挂载');
  
  // 监听成员变化事件
  if (TCIC.SDK.instance.on) {
    TCIC.SDK.instance.on('MemberJoin', handleMemberChange);
    TCIC.SDK.instance.on('MemberLeave', handleMemberChange);
    TCIC.SDK.instance.on('BoardPermissionChange', handleMemberChange);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  if (TCIC.SDK.instance.off) {
    TCIC.SDK.instance.off('MemberJoin', handleMemberChange);
    TCIC.SDK.instance.off('MemberLeave', handleMemberChange);
    TCIC.SDK.instance.off('BoardPermissionChange', handleMemberChange);
  }
});
</script>

<style lang="less" scoped>
.board-permission-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: modalSlideIn 0.3s ease-out;

  .board-permission-modal__content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    width: 90vw;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .board-permission-modal__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .board-permission-modal__controls {
      display: flex;
      align-items: center;
      gap: 10px;

      button {
        background: none;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        padding: 6px 12px;
        cursor: pointer;
        font-size: 14px;
        color: white;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  .board-permission-modal__body {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;

    .loading {
      text-align: center;
      padding: 60px 20px;
      color: #666;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      }
    }

    .no-members {
      text-align: center;
      padding: 60px 20px;
      color: #666;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }
    }

    .batch-controls {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e5e7eb;

      .batch-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;

        &.grant-all {
          background: #10b981;
          color: white;

          &:hover {
            background: #059669;
          }
        }

        &.revoke-all {
          background: #ef4444;
          color: white;

          &:hover {
            background: #dc2626;
          }
        }
      }
    }

    .member-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      margin-bottom: 12px;
      background: #f9fafb;

      .member-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .member-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .default-avatar {
            width: 100%;
            height: 100%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
          }
        }

        .member-details {
          .member-name {
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
          }

          .member-status {
            display: flex;
            gap: 8px;

            .role-tag {
              background: #e5e7eb;
              color: #6b7280;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
            }

            .permission-status {
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;

              &.granted {
                background: #d1fae5;
                color: #065f46;
              }

              &.denied {
                background: #fee2e2;
                color: #991b1b;
              }
            }
          }
        }
      }

      .member-actions {
        .action-btn {
          padding: 6px 12px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s ease;

          &.grant-btn {
            background: #10b981;
            color: white;

            &:hover {
              background: #059669;
            }
          }

          &.revoke-btn {
            background: #ef4444;
            color: white;

            &:hover {
              background: #dc2626;
            }
          }
        }
      }
    }
  }

  .board-permission-modal__footer {
    padding: 16px 24px;
    border-top: 1px solid #eee;
    background-color: #f8f9fa;

    .footer-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .board-permission-modal {
    .board-permission-modal__content {
      width: 95vw;
      max-height: 90vh;
    }

    .board-permission-modal__header {
      padding: 16px;

      h2 {
        font-size: 16px;
      }
    }

    .board-permission-modal__body {
      padding: 16px;

      .member-item {
        padding: 12px;

        .member-info {
          gap: 8px;

          .member-avatar {
            width: 32px;
            height: 32px;
          }

          .member-details {
            .member-name {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style>
