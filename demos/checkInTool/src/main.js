import { defineCustomElement } from 'vue';
import TeacherCheckInBtn from './TeacherCheckInBtn.ce.vue';
import StudentModal from './StudentModal.ce.vue';
import QuestionModal from './QuestionModal.ce.vue';
import FormulaEditorModal from './FormulaEditorModal.ce.vue';
import './nativeToolbarStyles.css';

const TeacherCheckInBtnEl = defineCustomElement(TeacherCheckInBtn, { shadowRoot: false });
customElements.define('teacher-checkin-btn', TeacherCheckInBtnEl);
const StudentModalEl = defineCustomElement(StudentModal, { shadowRoot: false });
customElements.define('student-modal', StudentModalEl);
const QuestionModalEl = defineCustomElement(QuestionModal, { shadowRoot: false });
customElements.define('question-modal', QuestionModalEl);
const FormulaEditorModalEl = defineCustomElement(FormulaEditorModal, { shadowRoot: false });
customElements.define('formula-editor-modal', FormulaEditorModalEl);

// 添加公式按钮到原生白板工具栏的函数
function addFormulaButtonToNativeToolbar() {
  // 等待白板工具栏加载
  const checkToolbar = () => {
    const toolbar = document.querySelector('.board-tools-component-menu.expand ul');
    if (toolbar) {
      // 检查是否已经添加过公式按钮
      if (toolbar.querySelector('.custom-formula-btn')) {
        return;
      }

      // 创建公式按钮的 li 元素
      const formulaLi = document.createElement('li');
      formulaLi.className = 'custom-formula-btn';
      formulaLi.style.paddingLeft = '7px';

      // 创建按钮元素
      const formulaBtn = document.createElement('button');
      formulaBtn.className = 'el-tooltip';
      formulaBtn.setAttribute('tabindex', '0');
      formulaBtn.setAttribute('title', '学科公式');

      // 创建公式图标 SVG
      formulaBtn.innerHTML = `
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="ic_tool_formula">
          <path d="M3 4h12M3 9h12M3 14h8M13 12l2 2 2-2" stroke="var(--icon-color, #fff)" stroke-width="1.5" stroke-linecap="round"/>
          <text x="9" y="10" text-anchor="middle" fill="var(--icon-color, #fff)" font-size="8" font-family="serif">∑</text>
        </svg>
      `;

      // 添加点击事件
      formulaBtn.addEventListener('click', () => {
        console.log('点击了原生工具栏中的公式按钮');

        // 切换active状态（模拟工具选择）
        const allToolItems = toolbar.querySelectorAll('li');
        allToolItems.forEach(item => item.classList.remove('active'));
        formulaLi.classList.add('active');

        // 触发公式编辑器显示
        const event = new CustomEvent('show-formula-editor', {
          bubbles: true,
          detail: {
            source: 'native-toolbar',
            callback: (formulaData) => {
              // 插入公式到白板
              if (window.teduBoard && window.TEduBoard) {
                try {
                  window.teduBoard.addElement(
                    window.TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_FORMULA,
                    { expression: formulaData.latex },
                    {
                      erasable: true,
                      left: `${formulaData.position.left}%`,
                      top: `${formulaData.position.top}%`,
                      fontSize: `${formulaData.style.fontSize}px`,
                      fontColor: formulaData.style.fontColor + 'FF',
                    }
                  );
                  console.log('公式已从原生工具栏插入到白板:', formulaData);

                  // 插入完成后移除active状态
                  setTimeout(() => {
                    formulaLi.classList.remove('active');
                  }, 300);
                } catch (error) {
                  console.error('插入公式失败:', error);
                }
              }
            }
          }
        });
        document.dispatchEvent(event);
      });

      // 将按钮添加到 li 中
      formulaLi.appendChild(formulaBtn);

      // 将 li 插入到倒数第三个位置
      const allItems = toolbar.children;
      const totalItems = allItems.length;

      if (totalItems >= 5) {
        // 如果有3个或更多项目，插入到倒数第三个位置
        const insertPosition = totalItems - 3; // 倒数第三个位置
        const referenceNode = allItems[insertPosition];
        toolbar.insertBefore(formulaLi, referenceNode);
      } else {
        // 如果少于3个项目，直接添加到末尾
        toolbar.appendChild(formulaLi);
      }

      console.log('公式按钮已添加到原生白板工具栏');
    } else {
      // 如果工具栏还没加载，继续等待
      setTimeout(checkToolbar, 500);
    }
  };

  // 开始检查工具栏
  checkToolbar();
}

TCIC.SDK.instance.promiseState('TStateComponentLoaded', true).then(() => {
  if (TCIC.SDK.instance.isTeacher()) {
    const menuArea = document.querySelector('.header-component .header__sub-operation');
    if (menuArea) {
      const checkInBtn = document.createElement('teacher-checkin-btn');
      menuArea.prepend(checkInBtn);
    }

    // 添加问题显示组件（仅教师可见）
    document.body.appendChild(document.createElement('question-modal'));
  }

  // 添加公式编辑器弹窗（全局单例）
  document.body.appendChild(document.createElement('formula-editor-modal'));

  // 添加公式按钮到原生白板工具栏
  addFormulaButtonToNativeToolbar();
});
