# 🚀 课前提问工具 - 生产环境部署指南

## 📦 构建生产版本

### 1. 本地构建
```bash
# 进入项目目录
cd demos/checkInTool

# 安装依赖（如果还没有安装）
npm install

# 构建生产版本
npm run build
```

构建完成后，会在 `dist/` 目录下生成：
- `custom.js` - 主要的JavaScript文件 (129KB)
- `custom.js.map` - Source Map文件（用于调试）

### 2. 文件说明
- **custom.js**: 包含所有功能的压缩版本，可直接用于生产环境
- **文件大小**: 约129KB（已压缩）
- **包含内容**: Vue 3 + 所有自定义组件 + 样式

## 🌐 部署到TCIC生产环境

### 方法1: 腾讯云自定义场景配置

1. **上传文件**
   - 登录腾讯云控制台
   - 进入实时互动-教育版
   - 找到"自定义场景配置"功能
   - 上传 `dist/custom.js` 文件

2. **配置场景**
   - 创建新的自定义场景
   - 设置JavaScript文件为上传的 `custom.js`
   - 保存配置

3. **应用到课堂**
   - 在创建课堂时选择自定义场景
   - 或在现有课堂中切换场景

### 方法2: CDN部署

1. **上传到CDN**
   ```bash
   # 将文件上传到您的CDN服务器
   # 例如：https://your-cdn.com/tcic/custom.js
   ```

2. **在课堂URL中使用**
   ```
   https://your-tcic-classroom.com/classroom?classid=123456&debugjs=https://your-cdn.com/tcic/custom.js
   ```

### 方法3: 自有服务器部署

1. **部署到服务器**
   ```bash
   # 复制文件到Web服务器
   cp dist/custom.js /var/www/html/tcic/
   ```

2. **配置CORS**（重要！）
   ```nginx
   # Nginx配置示例
   location /tcic/ {
       add_header Access-Control-Allow-Origin *;
       add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
       add_header Access-Control-Allow-Headers *;
   }
   ```

3. **在课堂URL中使用**
   ```
   https://your-tcic-classroom.com/classroom?classid=123456&debugjs=https://your-server.com/tcic/custom.js
   ```

## ⚙️ 配置说明

### 环境变量配置
如果需要修改API地址或其他配置，可以在构建前修改：

```javascript
// src/components/questionModal.vue
const apiUrl = `https://api.kaohaolaai.com/api/b/appoint/teacher/pre/question?classid=${classId}`;
```

### 默认ClassID配置
```javascript
// src/components/questionModal.vue - getClassId函数
return classInfo?.classId || '245783322'; // 修改默认值
```

## 🔧 功能验证

### 部署后测试清单

1. **老师端功能**
   - [ ] 工具栏显示"签到"和"提问"两个按钮
   - [ ] 点击"提问"按钮显示问题弹窗
   - [ ] 问题弹窗可以左右切换
   - [ ] 键盘方向键可以切换问题
   - [ ] 移动端支持滑动切换

2. **学生端功能**
   - [ ] 只显示签到相关功能
   - [ ] 不显示课前提问弹窗
   - [ ] 签到弹窗正常工作

3. **API集成**
   - [ ] 能正确获取classid
   - [ ] API请求正常发送
   - [ ] 问题数据正确显示

## 🐛 常见问题

### 1. 组件不显示
**原因**: TCIC SDK未正确加载或权限问题
**解决**: 检查控制台错误，确认用户角色

### 2. API请求失败
**原因**: CORS问题或网络问题
**解决**: 
- 检查API服务器CORS配置
- 确认网络连接
- 检查classid是否正确

### 3. 样式显示异常
**原因**: CSS冲突或加载问题
**解决**: 检查是否有CSS文件冲突

### 4. 移动端滑动不工作
**原因**: 触摸事件冲突
**解决**: 检查是否有其他组件阻止了触摸事件

## 📊 性能优化

### 文件大小优化
当前构建已经包含：
- ✅ 代码压缩
- ✅ Tree shaking
- ✅ 生产环境优化

### 加载性能
- 文件大小: 129KB（gzip后约40KB）
- 加载时间: 通常 < 1秒
- 初始化时间: < 500ms

## 🔄 更新部署

### 版本更新流程
1. 修改代码
2. 重新构建: `npm run build`
3. 上传新的 `custom.js` 文件
4. 清除CDN缓存（如果使用CDN）
5. 测试新功能

### 版本管理建议
```bash
# 为构建文件添加版本号
cp dist/custom.js dist/custom-v1.1.0.js
```

## 📞 技术支持

如果在部署过程中遇到问题：
1. 检查浏览器控制台错误信息
2. 确认TCIC SDK版本兼容性
3. 验证网络和CORS配置
4. 检查用户权限设置

---

**部署完成后，老师就可以在课堂中使用课前提问功能了！** 🎉
