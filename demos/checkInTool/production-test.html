<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课前提问工具 - 生产环境测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .test-section h3 {
            margin: 0 0 15px;
            color: #374151;
            font-size: 18px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .btn.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .status {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        
        .mock-toolbar {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            border: 2px dashed #d1d5db;
            margin: 15px 0;
            min-height: 50px;
            display: flex;
            align-items: center;
        }
        
        .header-component .header__sub-operation {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }
        
        .file-info strong {
            color: #1e40af;
        }
        
        .deployment-steps {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .deployment-steps h4 {
            margin: 0 0 15px;
            color: #92400e;
        }
        
        .deployment-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .deployment-steps li {
            margin: 8px 0;
            color: #78350f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 课前提问工具</h1>
            <p>生产环境测试页面 - 使用构建后的 custom.js 文件</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>📦 构建文件信息</h3>
                <div class="file-info">
                    <strong>文件路径:</strong> dist/custom.js<br>
                    <strong>文件大小:</strong> ~129KB (压缩后)<br>
                    <strong>构建时间:</strong> <span id="buildTime">检测中...</span><br>
                    <strong>包含组件:</strong> TeacherCheckInBtn, StudentModal, QuestionModal
                </div>
                <button class="btn" onclick="checkBuildFile()">🔍 检查构建文件</button>
            </div>
            
            <div class="test-section">
                <h3>🖥️ 模拟TCIC环境</h3>
                <p>以下模拟真实的TCIC课堂环境，使用生产构建文件：</p>
                <div class="header-component">
                    <div class="header__sub-operation mock-toolbar" id="mockToolbar">
                        <span style="color: #6b7280;">老师工具栏 →</span>
                        <!-- 组件将插入到这里 -->
                    </div>
                </div>
                <button class="btn success" onclick="loadProductionScript()">🚀 加载生产版本</button>
                <button class="btn" onclick="testTeacherFeatures()">👨‍🏫 测试老师功能</button>
                <button class="btn" onclick="testStudentFeatures()">👨‍🎓 测试学生功能</button>
            </div>
            
            <div class="test-section">
                <h3>🌐 部署到生产环境</h3>
                <div class="deployment-steps">
                    <h4>📋 部署步骤：</h4>
                    <ol>
                        <li>将 <code>dist/custom.js</code> 上传到您的CDN或服务器</li>
                        <li>确保服务器配置了正确的CORS头</li>
                        <li>在TCIC课堂URL中添加: <code>&debugjs=https://your-server.com/custom.js</code></li>
                        <li>测试老师和学生端功能</li>
                    </ol>
                </div>
                <button class="btn warning" onclick="showDeploymentInfo()">📖 查看详细部署指南</button>
                <button class="btn" onclick="testProductionURL()">🔗 测试生产URL</button>
            </div>
            
            <div class="test-section">
                <h3>🔧 功能测试</h3>
                <p>测试所有功能是否正常工作：</p>
                <button class="btn" onclick="testQuestionModal()">❓ 测试课前提问</button>
                <button class="btn" onclick="testSignInModal()">✅ 测试签到功能</button>
                <button class="btn" onclick="testMobileFeatures()">📱 测试移动端功能</button>
                <button class="btn" onclick="testAPIIntegration()">🔌 测试API集成</button>
            </div>
            
            <div id="statusArea"></div>
        </div>
    </div>

    <script>
        // 模拟TCIC SDK
        window.TCIC = {
            SDK: {
                instance: {
                    isTeacher: () => true,
                    isStudent: () => false,
                    promiseState: (state, value) => Promise.resolve(),
                    showMessageBox: (msg) => alert(msg)
                }
            }
        };

        function updateStatus(type, message) {
            const statusArea = document.getElementById('statusArea');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            statusArea.appendChild(statusDiv);
            statusArea.scrollTop = statusArea.scrollHeight;
        }

        function checkBuildFile() {
            updateStatus('info', '正在检查构建文件...');
            
            fetch('./dist/custom.js')
                .then(response => {
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        updateStatus('success', `构建文件存在，大小: ${size ? Math.round(size/1024) + 'KB' : '未知'}`);
                        
                        // 获取文件修改时间
                        const lastModified = response.headers.get('last-modified');
                        if (lastModified) {
                            document.getElementById('buildTime').textContent = new Date(lastModified).toLocaleString();
                        }
                    } else {
                        updateStatus('error', '构建文件不存在，请先运行 npm run build');
                    }
                })
                .catch(error => {
                    updateStatus('error', '无法访问构建文件: ' + error.message);
                });
        }

        function loadProductionScript() {
            updateStatus('info', '正在加载生产版本...');
            
            // 移除已存在的脚本
            const existingScript = document.querySelector('script[src*="custom.js"]');
            if (existingScript) {
                existingScript.remove();
            }

            // 清空工具栏
            const toolbar = document.getElementById('mockToolbar');
            const customElements = toolbar.querySelectorAll('teacher-checkin-btn, question-modal, student-modal');
            customElements.forEach(el => el.remove());

            // 加载生产脚本
            const script = document.createElement('script');
            script.src = './dist/custom.js?' + Date.now();
            script.onload = () => {
                updateStatus('success', '生产版本加载成功！');
                
                setTimeout(() => {
                    const teacherBtn = toolbar.querySelector('teacher-checkin-btn');
                    if (teacherBtn) {
                        updateStatus('success', '老师工具栏组件已添加');
                    } else {
                        updateStatus('error', '老师工具栏组件未找到');
                    }
                }, 1000);
            };
            script.onerror = () => {
                updateStatus('error', '无法加载生产版本，请确保构建文件存在');
            };
            document.head.appendChild(script);
        }

        function testTeacherFeatures() {
            updateStatus('info', '测试老师功能...');
            const teacherBtn = document.querySelector('teacher-checkin-btn');
            if (teacherBtn) {
                updateStatus('success', '老师组件已加载，可以点击工具栏按钮测试');
            } else {
                updateStatus('error', '请先加载生产版本');
            }
        }

        function testStudentFeatures() {
            updateStatus('info', '测试学生功能...');
            const studentModal = document.querySelector('student-modal');
            if (studentModal) {
                updateStatus('success', '学生组件已加载');
            } else {
                updateStatus('error', '学生组件未找到');
            }
        }

        function testQuestionModal() {
            updateStatus('info', '测试课前提问功能...');
            const questionModal = document.querySelector('question-modal');
            if (questionModal) {
                updateStatus('success', '问题组件已加载，点击老师工具栏的"提问"按钮测试');
            } else {
                updateStatus('error', '问题组件未找到');
            }
        }

        function testSignInModal() {
            updateStatus('info', '测试签到功能...');
            const teacherBtn = document.querySelector('teacher-checkin-btn');
            if (teacherBtn) {
                updateStatus('success', '签到组件已加载，点击老师工具栏的"签到"按钮测试');
            } else {
                updateStatus('error', '请先加载生产版本');
            }
        }

        function testMobileFeatures() {
            updateStatus('info', '测试移动端功能...');
            updateStatus('success', '移动端功能包括：触摸滑动切换问题、响应式布局');
        }

        function testAPIIntegration() {
            updateStatus('info', '测试API集成...');
            updateStatus('success', 'API地址: https://api.kaohaolaai.com/api/b/appoint/teacher/pre/question');
        }

        function showDeploymentInfo() {
            updateStatus('info', '详细部署指南请查看 DEPLOYMENT.md 文件');
            window.open('./DEPLOYMENT.md', '_blank');
        }

        function testProductionURL() {
            const url = prompt('请输入您的生产环境custom.js URL:', 'https://your-server.com/custom.js');
            if (url) {
                updateStatus('info', `测试URL: ${url}`);
                
                const script = document.createElement('script');
                script.src = url;
                script.onload = () => updateStatus('success', '生产URL加载成功！');
                script.onerror = () => updateStatus('error', '生产URL加载失败，请检查URL和CORS配置');
                document.head.appendChild(script);
            }
        }

        // 页面加载时自动检查构建文件
        window.onload = () => {
            checkBuildFile();
            updateStatus('info', '生产环境测试页面已加载，请点击"加载生产版本"开始测试');
        };
    </script>
</body>
</html>
