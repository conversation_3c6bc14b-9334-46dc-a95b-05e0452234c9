### 自定义签到与课前提问工具

本 demo 展示了如何借助 `useTask` hook 自定义一个集成签到和课前提问功能的工具。

## 🚀 快速开始

老师/学生在课堂URL下拼接`&debugjs=http://localhost:3001/custom.js`即可使用

## ✨ 功能特性

### 1. 签到功能
- 老师可以发起签到请求
- 学生收到签到弹窗提示
- 实时签到状态反馈

### 2. 课前提问显示 🆕
- 从API获取学生的课前提问
- 支持文本和图片类型的问题
- 实时刷新问题列表
- 响应式设计，适配移动端

## 🔧 技术实现

### API集成
- **接口地址**: `https://api.kaohaolaai.com/api/b/appoint/teacher/pre/question`
- **请求方式**: GET
- **参数**: `classid` (从URL参数或TCIC SDK获取)

### 组件结构
```
src/
├── components/
│   ├── teacherbtn.vue      # 老师工具栏按钮
│   ├── studentModal.vue    # 学生签到弹窗
│   └── questionModal.vue   # 课前提问显示弹窗 🆕
├── hooks/
│   └── useTask.ts          # TCIC任务通信Hook
└── main.js                 # 主入口文件
```

## 🎯 使用方法

### 老师端
1. 课堂界面会显示两个按钮：
   - **签到按钮**: 发起签到请求
   - **提问按钮**: 查看课前提问 🆝

2. 点击"提问"按钮会弹出问题列表弹窗
3. 可以查看学生的文本和图片提问
4. 支持刷新获取最新问题

### 学生端
1. 收到签到请求时会弹出签到确认弹窗
2. 可以查看老师发布的课前提问（如果权限允许）

## 📱 界面预览

### 老师工具栏
```
[📋 签到] [❓ 提问]
```

### 问题显示弹窗
- 标题栏：课前提问 + 刷新/关闭按钮
- 内容区：问题列表（文本+图片）
- 底部：问题统计信息

## 🔄 开发调试

1. 启动开发服务器：
```bash
npm run dev
```

2. 访问测试页面：
```
http://localhost:3001/demo.html    # 推荐：交互式演示页面
http://localhost:3001/test.html    # 详细功能说明页面
```

3. 在TCIC课堂中集成：
```
课堂URL + &debugjs=http://localhost:3001/custom.js
```

## ✨ 最新优化 (基于TCIC最佳实践)

### 学生签到组件优化
- **状态持久化**: 使用localStorage保存签到状态，刷新页面不丢失
- **TCIC状态检查**: 等待设备检测和课堂状态就绪后再显示弹窗
- **原生消息提示**: 使用TCIC.SDK.instance.showMessageBox显示系统级提示
- **加载状态**: 添加签到过程中的loading状态和动画
- **错误处理**: 完善的错误处理和状态恢复机制
- **现代化UI**: 渐变背景、动画效果、响应式设计

### 技术改进
- **生命周期管理**: 正确使用onMounted和onUnmounted
- **状态管理**: 更完善的本地状态管理和同步
- **用户体验**: 防重复操作、加载提示、成功反馈
- **代码质量**: 更好的错误处理和类型安全

## 📋 API数据格式

```json
{
  "code": 100000,
  "msg": ["操作成功"],
  "data": [
    [
      {
        "message": "老师你好，我是李洛伊",
        "message_type": "text",
        "content": "老师你好，我是李洛伊",
        "role": "user",
        "is_system": "2"
      },
      {
        "message": "https://example.com/image.png",
        "message_type": "image",
        "content": "https://example.com/image.png",
        "role": "user",
        "is_system": "2"
      }
    ]
  ]
}
```

## 🛠️ 自定义配置

### 修改默认classid
在 `questionModal.vue` 中修改 `getClassId()` 函数的默认值

### 修改API地址
在 `questionModal.vue` 中修改 `fetchQuestions()` 函数中的API URL

### 样式定制
修改各组件的 `<style>` 部分来自定义界面样式
